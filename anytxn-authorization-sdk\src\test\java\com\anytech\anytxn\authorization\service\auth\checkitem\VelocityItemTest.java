package com.anytech.anytxn.authorization.service.auth.checkitem;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.domain.dto.ParmAuthCheckControlDTO;
import com.anytech.anytxn.authorization.mapper.cardspecialvel.CardSpecialVelocityControlSelfMapper;
import com.anytech.anytxn.authorization.service.manager.AuthCheckItemManager;
import com.anytech.anytxn.authorization.service.rule.RuleTransferImpl;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoSelfMapper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityControlSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;

/**
 * @description 速度检查项测试类
 * <AUTHOR>
 * @date 2025/07/14
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
class VelocityItemTest {

    @InjectMocks
    private VelocityItem velocityItem;

    @Mock
    private CardSpecialVelocityControlSelfMapper cardSpecialVelocityControlSelfMapper;

    @Mock
    private AuthCheckItemManager authCheckItemManager;

    @Mock
    private AccountStatisticsInfoSelfMapper accountStatisticsInfoSelfMapper;

    @Mock
    private RuleTransferImpl ruleTransferService;

    @Mock
    private ParmVelocityControlSelfMapper parmVelocityControlSelfMapper;

    private AuthorizationCheckProcessingPayload payload;
    private ParmAuthCheckControlDTO checkControlDTO;

    @BeforeEach
    void setUp() {
        payload = new AuthorizationCheckProcessingPayload();
        checkControlDTO = new ParmAuthCheckControlDTO();

        // 使用MockedStatic模拟OrgNumberUtils静态依赖，避免AuthRecordedDTO和CardAuthorizationDTO构造函数中的NullPointerException
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // 设置基础数据
            AuthRecordedDTO authRecordedDTO = new AuthRecordedDTO();
            authRecordedDTO.setAuthTransactionAmount(new BigDecimal("100.00"));
            payload.setAuthRecordedDTO(authRecordedDTO);

            CardAuthorizationDTO cardAuthorizationDTO = new CardAuthorizationDTO();
            cardAuthorizationDTO.setCardNumber("1234567890123456");
            payload.setCardAuthorizationDTO(cardAuthorizationDTO);

            // 设置CardProductInfoResDTO
            CardProductInfoResDTO cardProductInfoResDTO = new CardProductInfoResDTO();
            cardProductInfoResDTO.setProductNumber("PROD001");
            payload.setCardProductInfo(cardProductInfoResDTO);

            // 设置组织信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setOrganizationNumber("001");
            payload.setOrgInfo(orgInfo);
        }

        // Mock authCheckItemManager.getAccountManagementInfoDTO方法
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            AccountManagementInfoDTO accountManagementInfoDTO = new AccountManagementInfoDTO();
            accountManagementInfoDTO.setAccountManagementId("ACC001");
            lenient().when(authCheckItemManager.getAccountManagementInfoDTO(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(accountManagementInfoDTO);
        }
    }

    @Test
    @DisplayName("测试速度检查 - 基础测试")
    void testCheck_Basic() {
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // When
            Integer result = velocityItem.check(payload, checkControlDTO);

            // Then
            assertNotNull(result);
        }
    }

    @Test
    @DisplayName("测试授权速度检查 - 基础测试")
    void testAuthorizationVelocityCheck_Basic() {
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // When
            int result = velocityItem.authorizationVelocityCheck(payload, checkControlDTO);

            // Then
            assertNotNull(result);
        }
    }

    @Test
    @DisplayName("测试授权速度检查 - 零金额交易")
    void testAuthorizationVelocityCheck_ZeroAmount() {
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Given
            AuthRecordedDTO authRecordedDTO = payload.getAuthRecordedDTO();
            authRecordedDTO.setAuthTransactionAmount(BigDecimal.ZERO);

            // When
            int result = velocityItem.authorizationVelocityCheck(payload, checkControlDTO);

            // Then
            assertNotNull(result);
        }
    }

    @Test
    @DisplayName("测试授权速度检查 - 大金额交易")
    void testAuthorizationVelocityCheck_LargeAmount() {
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Given
            AuthRecordedDTO authRecordedDTO = payload.getAuthRecordedDTO();
            authRecordedDTO.setAuthTransactionAmount(new BigDecimal("999999.99"));

            // When
            int result = velocityItem.authorizationVelocityCheck(payload, checkControlDTO);

            // Then
            assertNotNull(result);
        }
    }

    @Test
    @DisplayName("测试授权速度检查 - 空的AuthRecordedDTO")
    void testAuthorizationVelocityCheck_NullAuthRecordedDTO() {
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Given
            payload.setAuthRecordedDTO(null);

            // When & Then
            assertThrows(Exception.class, () -> {
                velocityItem.authorizationVelocityCheck(payload, checkControlDTO);
            });
        }
    }

    @Test
    @DisplayName("测试授权速度检查 - 空的CardAuthorizationDTO")
    void testAuthorizationVelocityCheck_NullCardAuthorizationDTO() {
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Given
            payload.setCardAuthorizationDTO(null);

            // When & Then
            assertThrows(Exception.class, () -> {
                velocityItem.authorizationVelocityCheck(payload, checkControlDTO);
            });
        }
    }

    @Test
    @DisplayName("测试授权速度检查 - 设置交易类型代码")
    void testAuthorizationVelocityCheck_WithTransactionTypeCodes() {
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Given
            AuthRecordedDTO authRecordedDTO = payload.getAuthRecordedDTO();
            authRecordedDTO.setAuthTransactionTypeTopCode("01");
            authRecordedDTO.setAuthTransactionTypeDetailCode("001");
            authRecordedDTO.setAuthTransactionCurrencyCode("USD");

            // When
            int result = velocityItem.authorizationVelocityCheck(payload, checkControlDTO);

            // Then
            assertNotNull(result);
        }
    }

    @Test
    @DisplayName("测试授权速度检查 - 设置商户信息")
    void testAuthorizationVelocityCheck_WithMerchantInfo() {
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Given
            AuthRecordedDTO authRecordedDTO = payload.getAuthRecordedDTO();
            authRecordedDTO.setAuthMerchantType("5411");
            authRecordedDTO.setMerchantId("MERCHANT001");

            // When
            int result = velocityItem.authorizationVelocityCheck(payload, checkControlDTO);

            // Then
            assertNotNull(result);
        }
    }

    @Test
    @DisplayName("测试授权速度检查 - 设置客户ID")
    void testAuthorizationVelocityCheck_WithCustomerId() {
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Given
            AuthRecordedDTO authRecordedDTO = payload.getAuthRecordedDTO();
            authRecordedDTO.setAuthCustomerId("CUST001");

            // When
            int result = velocityItem.authorizationVelocityCheck(payload, checkControlDTO);

            // Then
            assertNotNull(result);
        }
    }

    @Test
    @DisplayName("测试授权速度检查 - 负金额交易")
    void testAuthorizationVelocityCheck_NegativeAmount() {
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Given
            AuthRecordedDTO authRecordedDTO = payload.getAuthRecordedDTO();
            authRecordedDTO.setAuthTransactionAmount(new BigDecimal("-100.00"));

            // When
            int result = velocityItem.authorizationVelocityCheck(payload, checkControlDTO);

            // Then
            assertNotNull(result);
        }
    }

    @Test
    @DisplayName("测试授权速度检查 - 设置卡产品信息")
    void testAuthorizationVelocityCheck_WithCardProductInfo() {
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Given
            CardProductInfoResDTO cardProductInfo = payload.getCardProductInfo();
            cardProductInfo.setProductNumber("PROD002");

            // When
            int result = velocityItem.authorizationVelocityCheck(payload, checkControlDTO);

            // Then
            assertNotNull(result);
        }
    }

    @Test
    @DisplayName("测试授权速度检查 - 设置组织信息")
    void testAuthorizationVelocityCheck_WithOrgInfo() {
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Given
            OrganizationInfoResDTO orgInfo = payload.getOrgInfo();
            orgInfo.setOrganizationNumber("002");

            // When
            int result = velocityItem.authorizationVelocityCheck(payload, checkControlDTO);

            // Then
            assertNotNull(result);
        }
    }
}