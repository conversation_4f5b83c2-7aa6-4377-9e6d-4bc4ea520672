package com.anytech.anytxn.authorization.service.auth.comm;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSONObject;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.domain.dto.AuthFeeDTO;
import com.anytech.anytxn.authorization.base.service.auth.IAuthPrePostLogService;
import com.anytech.anytxn.authorization.base.service.manager.ApplicationManager;
import com.anytech.anytxn.authorization.base.utils.TimeZoneUtil;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthPrePostLogDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.OutstandingTransactionDTO;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTransTypeEnum;
import com.anytech.anytxn.business.base.authorization.enums.PostFlagEnum;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @description AuthPrePostInfoServiceModifyImpl的单元测试类
 * <AUTHOR>
 * @date 2025/07/09
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("授权预入账信息修改服务实现测试")
class AuthPrePostInfoServiceModifyImplTest {

    @Mock
    private IAuthPrePostLogService authPrePostLogService;

    @Mock
    private SequenceIdGen sequenceIdGen;

    @InjectMocks
    private AuthPrePostInfoServiceModifyImpl authPrePostInfoServiceModifyImpl;

    private AuthorizationCheckProcessingPayload payload;
    private OutstandingTransactionDTO outstandingTransactionDTO;
    private AuthRecordedDTO authRecordedDTO;
    private CardAuthorizationDTO cardAuthorizationDTO;
    private OrganizationInfoResDTO orgInfo;
    private AuthPrePostLogDTO authPrePostLogDTO;

    @BeforeEach
    void setUp() {
        // Mock OrgNumberUtils静态字段
        OrgNumberUtils.orgNumberUtil = mock(OrgNumberUtils.class);
        lenient().when(OrgNumberUtils.getOrg()).thenReturn("001");
        lenient().when(OrgNumberUtils.orgNumberUtil.getBatchOrg()).thenReturn("001");

        // 初始化测试数据
        payload = new AuthorizationCheckProcessingPayload();
        
        outstandingTransactionDTO = new OutstandingTransactionDTO();
        outstandingTransactionDTO.setDebitCreditIndcator("D");
        
        authRecordedDTO = new AuthRecordedDTO();
        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
        authRecordedDTO.setAuthGlobalFlowNumber("123456789");
        authRecordedDTO.setAuthOriginalGlobalFlowNumber("987654321");
        authRecordedDTO.setAuthCardNumber("1234567890123456");
        authRecordedDTO.setAuthTransactionTypeTopCode("01");
        authRecordedDTO.setAuthTransactionTypeDetailCode("001");
        authRecordedDTO.setSourceCode("ATM");
        authRecordedDTO.setTransactionDesc("测试交易");
        authRecordedDTO.setAuthMerchantCountryCode("156");
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("1000.00"));
        authRecordedDTO.setAuthCardholderBillingAmount(new BigDecimal("1000.00"));
        authRecordedDTO.setAuthCardholderOriginalBillingAmount(new BigDecimal("1000.00"));
        authRecordedDTO.setAuthTransactionSettlementAmount(new BigDecimal("1000.00"));
        authRecordedDTO.setAuthTransactionCurrencyCode("156");
        authRecordedDTO.setAuthBillingCurrencyCode("156");
        authRecordedDTO.setSettlementCurrencyCode("156");
        authRecordedDTO.setBillingConversionRate("1.0");
        authRecordedDTO.setSettlementConversionRate("1.0");
        authRecordedDTO.setAuthServicePointCardCode("05");
        authRecordedDTO.setAuthServicePointPinCode("1");
        authRecordedDTO.setAuthServicePointConditionCode("00");
        authRecordedDTO.setSystemTransactionTime(LocalDateTime.now());
        authRecordedDTO.setAuthSystemTraceAuditNumber("123456");
        authRecordedDTO.setAuthCardExpirationDate("2512");
        authRecordedDTO.setAuthMerchantType("5411");
        authRecordedDTO.setAuthForwardingIdentificationCode("123456");
        authRecordedDTO.setAuthAcquiringIdentificationCode("654321");
        authRecordedDTO.setAuthRetrievalReferenceNumber("123456789012");
        authRecordedDTO.setAuthLocalTransactionDate("1225");
        authRecordedDTO.setAuthLocalTransactionTime("120000");
        authRecordedDTO.setAuthTransmissionTime("12251200");
        authRecordedDTO.setAuthCardAcceptorTerminalCode("12345678");
        authRecordedDTO.setAuthCardAcceptorNameLocation("测试商户");
        authRecordedDTO.setMerchantInfo2ndCode("MERCHANT2");
        authRecordedDTO.setMerchantInfo2ndName("第二商户");
        authRecordedDTO.setAuthMobileNo("13800138000");
        authRecordedDTO.setAuthNetworkReferenceId("NET123456");
        authRecordedDTO.setAuthAuthCode("123456");
        authRecordedDTO.setAuthResponseCode("00");
        
        cardAuthorizationDTO = new CardAuthorizationDTO();
        cardAuthorizationDTO.setProductNumber("PROD001");
        cardAuthorizationDTO.setPrimaryCustomerId("CUST001");
        cardAuthorizationDTO.setExpireDate("2512");
        
        orgInfo = new OrganizationInfoResDTO();
        orgInfo.setOrganizationNumber("ORG001");
        orgInfo.setNextProcessingDay(LocalDate.now());
        
        authPrePostLogDTO = new AuthPrePostLogDTO();
        authPrePostLogDTO.setId("123");
        authPrePostLogDTO.setProcessFlag(PostFlagEnum.UNPROCESSED.getCode());
        
        payload.setOutstandingTransactionDTO(outstandingTransactionDTO);
        payload.setAuthRecordedDTO(authRecordedDTO);
        payload.setCardAuthorizationDTO(cardAuthorizationDTO);
        payload.setOrgInfo(orgInfo);
        payload.setAuthPrePostLogDTO(authPrePostLogDTO);
    }

    @Test
    @DisplayName("修改授权预入账信息-成功场景")
    void testModifyAuthPrePostInfo_Success() {
        // 准备数据
        when(sequenceIdGen.generateId(anyString())).thenReturn("123456");
        
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ApplicationManager> appManagerMock = mockStatic(ApplicationManager.class);
             MockedStatic<TimeZoneUtil> timeZoneUtilMock = mockStatic(TimeZoneUtil.class);
             MockedStatic<LocalDateTimeUtil> localDateTimeUtilMock = mockStatic(LocalDateTimeUtil.class);
             MockedStatic<CustAccountBO> custAccountBOMock = mockStatic(CustAccountBO.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            appManagerMock.when(() -> ApplicationManager.getTransTimeOfMastercard(any(), any()))
                    .thenReturn(LocalDateTime.now());
            timeZoneUtilMock.when(() -> TimeZoneUtil.timeZone(any(), any())).thenReturn(new BigDecimal("8.0"));
            localDateTimeUtilMock.when(() -> LocalDateTimeUtil.parse(anyString(), anyString()))
                    .thenReturn(LocalDateTime.now());
            custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
            
            when(authPrePostLogService.insert(any(AuthPrePostLogDTO.class))).thenReturn(1);
            when(authPrePostLogService.update(any(AuthPrePostLogDTO.class))).thenReturn(1);
            
            // 执行测试
            int result = authPrePostInfoServiceModifyImpl.modifyAuthPrePostInfo(payload);
            
            // 验证结果
            assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
            verify(authPrePostLogService).insert(any(AuthPrePostLogDTO.class));
            verify(authPrePostLogService).update(any(AuthPrePostLogDTO.class));
        }
    }

    @Test
    @DisplayName("修改授权预入账信息-构建失败")
    void testModifyAuthPrePostInfo_BuildFailed() {
        // 准备数据
        when(sequenceIdGen.generateId(anyString())).thenReturn("123456");
        
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ApplicationManager> appManagerMock = mockStatic(ApplicationManager.class);
             MockedStatic<TimeZoneUtil> timeZoneUtilMock = mockStatic(TimeZoneUtil.class);
             MockedStatic<LocalDateTimeUtil> localDateTimeUtilMock = mockStatic(LocalDateTimeUtil.class);
             MockedStatic<CustAccountBO> custAccountBOMock = mockStatic(CustAccountBO.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            appManagerMock.when(() -> ApplicationManager.getTransTimeOfMastercard(any(), any()))
                    .thenReturn(LocalDateTime.now());
            timeZoneUtilMock.when(() -> TimeZoneUtil.timeZone(any(), any())).thenReturn(new BigDecimal("8.0"));
            localDateTimeUtilMock.when(() -> LocalDateTimeUtil.parse(anyString(), anyString()))
                    .thenReturn(LocalDateTime.now());
            custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
            
            when(authPrePostLogService.insert(any(AuthPrePostLogDTO.class))).thenReturn(0);
            
            // 执行测试
            int result = authPrePostInfoServiceModifyImpl.modifyAuthPrePostInfo(payload);
            
            // 验证结果
            assertEquals(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode(), result);
            verify(authPrePostLogService).insert(any(AuthPrePostLogDTO.class));
            verify(authPrePostLogService, never()).update(any(AuthPrePostLogDTO.class));
        }
    }

    @Test
    @DisplayName("修改授权预入账信息-更新失败")
    void testModifyAuthPrePostInfo_UpdateFailed() {
        // 准备数据
        when(sequenceIdGen.generateId(anyString())).thenReturn("123456");
        
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ApplicationManager> appManagerMock = mockStatic(ApplicationManager.class);
             MockedStatic<TimeZoneUtil> timeZoneUtilMock = mockStatic(TimeZoneUtil.class);
             MockedStatic<LocalDateTimeUtil> localDateTimeUtilMock = mockStatic(LocalDateTimeUtil.class);
             MockedStatic<CustAccountBO> custAccountBOMock = mockStatic(CustAccountBO.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            appManagerMock.when(() -> ApplicationManager.getTransTimeOfMastercard(any(), any()))
                    .thenReturn(LocalDateTime.now());
            timeZoneUtilMock.when(() -> TimeZoneUtil.timeZone(any(), any())).thenReturn(new BigDecimal("8.0"));
            localDateTimeUtilMock.when(() -> LocalDateTimeUtil.parse(anyString(), anyString()))
                    .thenReturn(LocalDateTime.now());
            custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
            
            when(authPrePostLogService.insert(any(AuthPrePostLogDTO.class))).thenReturn(1);
            when(authPrePostLogService.update(any(AuthPrePostLogDTO.class))).thenReturn(0);
            
            // 执行测试
            int result = authPrePostInfoServiceModifyImpl.modifyAuthPrePostInfo(payload);
            
            // 验证结果
            assertEquals(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode(), result);
            verify(authPrePostLogService).insert(any(AuthPrePostLogDTO.class));
            verify(authPrePostLogService).update(any(AuthPrePostLogDTO.class));
        }
    }

    @Test
    @DisplayName("构建授权预入账信息-成功场景")
    void testBuildAuthPrePostInfo_Success() {
        // 准备数据
        when(sequenceIdGen.generateId(anyString())).thenReturn("123456");
        
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ApplicationManager> appManagerMock = mockStatic(ApplicationManager.class);
             MockedStatic<TimeZoneUtil> timeZoneUtilMock = mockStatic(TimeZoneUtil.class);
             MockedStatic<LocalDateTimeUtil> localDateTimeUtilMock = mockStatic(LocalDateTimeUtil.class);
             MockedStatic<CustAccountBO> custAccountBOMock = mockStatic(CustAccountBO.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            appManagerMock.when(() -> ApplicationManager.getTransTimeOfMastercard(any(), any()))
                    .thenReturn(LocalDateTime.now());
            timeZoneUtilMock.when(() -> TimeZoneUtil.timeZone(any(), any())).thenReturn(new BigDecimal("8.0"));
            localDateTimeUtilMock.when(() -> LocalDateTimeUtil.parse(anyString(), anyString()))
                    .thenReturn(LocalDateTime.now());
            custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
            
            when(authPrePostLogService.insert(any(AuthPrePostLogDTO.class))).thenReturn(1);
            
            // 执行测试
            int result = authPrePostInfoServiceModifyImpl.buildAuthPrePostInfo(payload);
            
            // 验证结果
            assertEquals(1, result);
            verify(authPrePostLogService).insert(any(AuthPrePostLogDTO.class));
            assertNotNull(payload.getAuthPrePostLogDTO());
        }
    }



    @Test
    @DisplayName("构建授权预入账信息-包含费用JSON")
    void testBuildAuthPrePostInfo_WithFeeJson() {
        // 准备数据
        AuthFeeDTO authFeeDTO = new AuthFeeDTO();
        authFeeDTO.setAuthDccFee(new BigDecimal("10.00"));
        authFeeDTO.setQueryTransFee(new BigDecimal("5.00"));
        authFeeDTO.setCashTransFee(new BigDecimal("8.00"));
        authFeeDTO.setAuthConsumeFee(new BigDecimal("12.00"));
        
        String feeJson = JSONObject.toJSONString(authFeeDTO);
        authRecordedDTO.setAuthFeeJson(feeJson);
        
        when(sequenceIdGen.generateId(anyString())).thenReturn("123456");
        
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ApplicationManager> appManagerMock = mockStatic(ApplicationManager.class);
             MockedStatic<TimeZoneUtil> timeZoneUtilMock = mockStatic(TimeZoneUtil.class);
             MockedStatic<LocalDateTimeUtil> localDateTimeUtilMock = mockStatic(LocalDateTimeUtil.class);
             MockedStatic<CustAccountBO> custAccountBOMock = mockStatic(CustAccountBO.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            appManagerMock.when(() -> ApplicationManager.getTransTimeOfMastercard(any(), any()))
                    .thenReturn(LocalDateTime.now());
            timeZoneUtilMock.when(() -> TimeZoneUtil.timeZone(any(), any())).thenReturn(new BigDecimal("8.0"));
            localDateTimeUtilMock.when(() -> LocalDateTimeUtil.parse(anyString(), anyString()))
                    .thenReturn(LocalDateTime.now());
            custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
            
            when(authPrePostLogService.insert(any(AuthPrePostLogDTO.class))).thenReturn(1);
            
            // 执行测试
            int result = authPrePostInfoServiceModifyImpl.buildAuthPrePostInfo(payload);
            
            // 验证结果
            assertEquals(1, result);
            verify(authPrePostLogService).insert(any(AuthPrePostLogDTO.class));
            
            AuthPrePostLogDTO resultDTO = payload.getAuthPrePostLogDTO();
            assertNotNull(resultDTO);
            assertEquals(authFeeDTO.getAuthDccFee(), resultDTO.getAuthDccFee());
            assertEquals(authFeeDTO.getQueryTransFee(), resultDTO.getQueryTransFee());
            assertEquals(authFeeDTO.getCashTransFee(), resultDTO.getCashTransFee());
            assertEquals(authFeeDTO.getAuthConsumeFee(), resultDTO.getAuthConsumeFee());
        }
    }

    @Test
    @DisplayName("构建授权预入账信息-空费用JSON")
    void testBuildAuthPrePostInfo_EmptyFeeJson() {
        // 准备数据
        authRecordedDTO.setAuthFeeJson(null);
        when(sequenceIdGen.generateId(anyString())).thenReturn("123456");
        
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ApplicationManager> appManagerMock = mockStatic(ApplicationManager.class);
             MockedStatic<TimeZoneUtil> timeZoneUtilMock = mockStatic(TimeZoneUtil.class);
             MockedStatic<LocalDateTimeUtil> localDateTimeUtilMock = mockStatic(LocalDateTimeUtil.class);
             MockedStatic<CustAccountBO> custAccountBOMock = mockStatic(CustAccountBO.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            appManagerMock.when(() -> ApplicationManager.getTransTimeOfMastercard(any(), any()))
                    .thenReturn(LocalDateTime.now());
            timeZoneUtilMock.when(() -> TimeZoneUtil.timeZone(any(), any())).thenReturn(new BigDecimal("8.0"));
            localDateTimeUtilMock.when(() -> LocalDateTimeUtil.parse(anyString(), anyString()))
                    .thenReturn(LocalDateTime.now());
            custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
            
            when(authPrePostLogService.insert(any(AuthPrePostLogDTO.class))).thenReturn(1);
            
            // 执行测试
            int result = authPrePostInfoServiceModifyImpl.buildAuthPrePostInfo(payload);
            
            // 验证结果
            assertEquals(1, result);
            verify(authPrePostLogService).insert(any(AuthPrePostLogDTO.class));
            
            AuthPrePostLogDTO resultDTO = payload.getAuthPrePostLogDTO();
            assertNotNull(resultDTO);
            assertNull(resultDTO.getAuthDccFee());
            assertNull(resultDTO.getQueryTransFee());
            assertNull(resultDTO.getCashTransFee());
            assertNull(resultDTO.getAuthConsumeFee());
        }
    }

    @Test
    @DisplayName("构建授权预入账信息-交易类型为空异常")
    void testBuildAuthPrePostInfo_NullAuthTransType() {
        // 准备数据
        authRecordedDTO.setAuthTransactionTypeCode(null);
        lenient().when(sequenceIdGen.generateId(anyString())).thenReturn("123456");

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            lenient().when(TenantUtils.getTenantId()).thenReturn("TENANT001");

            // 执行测试并验证异常
            assertThrows(NullPointerException.class, () -> {
                authPrePostInfoServiceModifyImpl.buildAuthPrePostInfo(payload);
            });
        }
    }

    @Test
    @DisplayName("构建授权预入账信息-数据库插入失败")
    void testBuildAuthPrePostInfo_InsertFailed() {
        // 准备数据
        when(sequenceIdGen.generateId(anyString())).thenReturn("123456");
        
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ApplicationManager> appManagerMock = mockStatic(ApplicationManager.class);
             MockedStatic<TimeZoneUtil> timeZoneUtilMock = mockStatic(TimeZoneUtil.class);
             MockedStatic<LocalDateTimeUtil> localDateTimeUtilMock = mockStatic(LocalDateTimeUtil.class);
             MockedStatic<CustAccountBO> custAccountBOMock = mockStatic(CustAccountBO.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            appManagerMock.when(() -> ApplicationManager.getTransTimeOfMastercard(any(), any()))
                    .thenReturn(LocalDateTime.now());
            timeZoneUtilMock.when(() -> TimeZoneUtil.timeZone(any(), any())).thenReturn(new BigDecimal("8.0"));
            localDateTimeUtilMock.when(() -> LocalDateTimeUtil.parse(anyString(), anyString()))
                    .thenReturn(LocalDateTime.now());
            custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
            
            when(authPrePostLogService.insert(any(AuthPrePostLogDTO.class))).thenReturn(0);
            
            // 执行测试
            int result = authPrePostInfoServiceModifyImpl.buildAuthPrePostInfo(payload);
            
            // 验证结果
            assertEquals(0, result);
            verify(authPrePostLogService).insert(any(AuthPrePostLogDTO.class));
        }
    }

    @Test
    @DisplayName("构建授权预入账信息-验证字段设置完整性")
    void testBuildAuthPrePostInfo_FieldMapping() {
        // 准备数据
        when(sequenceIdGen.generateId(anyString())).thenReturn("123456");
        
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ApplicationManager> appManagerMock = mockStatic(ApplicationManager.class);
             MockedStatic<TimeZoneUtil> timeZoneUtilMock = mockStatic(TimeZoneUtil.class);
             MockedStatic<LocalDateTimeUtil> localDateTimeUtilMock = mockStatic(LocalDateTimeUtil.class);
             MockedStatic<CustAccountBO> custAccountBOMock = mockStatic(CustAccountBO.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            appManagerMock.when(() -> ApplicationManager.getTransTimeOfMastercard(any(), any()))
                    .thenReturn(LocalDateTime.now());
            timeZoneUtilMock.when(() -> TimeZoneUtil.timeZone(any(), any())).thenReturn(new BigDecimal("8.0"));
            localDateTimeUtilMock.when(() -> LocalDateTimeUtil.parse(anyString(), anyString()))
                    .thenReturn(LocalDateTime.now());
            custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
            
            when(authPrePostLogService.insert(any(AuthPrePostLogDTO.class))).thenReturn(1);
            
            // 执行测试
            int result = authPrePostInfoServiceModifyImpl.buildAuthPrePostInfo(payload);
            
            // 验证结果
            assertEquals(1, result);
            
            AuthPrePostLogDTO resultDTO = payload.getAuthPrePostLogDTO();
            assertNotNull(resultDTO);
            assertEquals(authRecordedDTO.getAuthGlobalFlowNumber(), resultDTO.getAuthGlobalFlowNumber());
            assertEquals(authRecordedDTO.getAuthCardNumber(), resultDTO.getCardNumber());
            assertEquals(orgInfo.getOrganizationNumber(), resultDTO.getOrganizationNumber());
            assertEquals(cardAuthorizationDTO.getProductNumber(), resultDTO.getProductNumber());
            assertEquals(cardAuthorizationDTO.getPrimaryCustomerId(), resultDTO.getCustomerId());
            assertEquals(authRecordedDTO.getAuthTransactionAmount(), resultDTO.getTransactionAmount());
            assertEquals(authRecordedDTO.getAuthTransactionCurrencyCode(), resultDTO.getTransactionCurrencyCode());
            assertEquals(PostFlagEnum.UNPROCESSED.getCode(), resultDTO.getProcessFlag());
            assertEquals(1L, resultDTO.getVersionNumber());
        }
    }

    @Test
    @DisplayName("构建授权预入账信息-POS条目模式组合")
    void testBuildAuthPrePostInfo_PosEntryMode() {
        // 准备数据
        authRecordedDTO.setAuthServicePointCardCode("05");
        authRecordedDTO.setAuthServicePointPinCode("1");
        when(sequenceIdGen.generateId(anyString())).thenReturn("123456");
        
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ApplicationManager> appManagerMock = mockStatic(ApplicationManager.class);
             MockedStatic<TimeZoneUtil> timeZoneUtilMock = mockStatic(TimeZoneUtil.class);
             MockedStatic<LocalDateTimeUtil> localDateTimeUtilMock = mockStatic(LocalDateTimeUtil.class);
             MockedStatic<CustAccountBO> custAccountBOMock = mockStatic(CustAccountBO.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            appManagerMock.when(() -> ApplicationManager.getTransTimeOfMastercard(any(), any()))
                    .thenReturn(LocalDateTime.now());
            timeZoneUtilMock.when(() -> TimeZoneUtil.timeZone(any(), any())).thenReturn(new BigDecimal("8.0"));
            localDateTimeUtilMock.when(() -> LocalDateTimeUtil.parse(anyString(), anyString()))
                    .thenReturn(LocalDateTime.now());
            custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
            
            when(authPrePostLogService.insert(any(AuthPrePostLogDTO.class))).thenReturn(1);
            
            // 执行测试
            int result = authPrePostInfoServiceModifyImpl.buildAuthPrePostInfo(payload);
            
            // 验证结果
            assertEquals(1, result);
            
            AuthPrePostLogDTO resultDTO = payload.getAuthPrePostLogDTO();
            assertNotNull(resultDTO);
            assertEquals("051", resultDTO.getPosEntryMode());
        }
    }



    @Test
    @DisplayName("构建授权预入账信息-AuthTransTypeEnum为空异常")
    void testBuildAuthPrePostInfo_NullAuthTransTypeEnum() {
        // 准备数据 - 设置一个无效的交易类型代码
        authRecordedDTO.setAuthTransactionTypeCode("INVALID_CODE");
        lenient().when(sequenceIdGen.generateId(anyString())).thenReturn("123456");

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ApplicationManager> appManagerMock = mockStatic(ApplicationManager.class);
             MockedStatic<TimeZoneUtil> timeZoneUtilMock = mockStatic(TimeZoneUtil.class);
             MockedStatic<LocalDateTimeUtil> localDateTimeUtilMock = mockStatic(LocalDateTimeUtil.class);
             MockedStatic<CustAccountBO> custAccountBOMock = mockStatic(CustAccountBO.class)) {

            lenient().when(TenantUtils.getTenantId()).thenReturn("TENANT001");
            lenient().when(ApplicationManager.getTransTimeOfMastercard(any(), any()))
                    .thenReturn(LocalDateTime.now());
            lenient().when(TimeZoneUtil.timeZone(any(), any())).thenReturn(new BigDecimal("8.0"));
            lenient().when(LocalDateTimeUtil.parse(anyString(), anyString()))
                    .thenReturn(LocalDateTime.now());
            custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);

            // 执行测试并验证异常
            assertThrows(NullPointerException.class, () -> {
                authPrePostInfoServiceModifyImpl.buildAuthPrePostInfo(payload);
            });
        }
    }

    @Test
    @DisplayName("构建授权预入账信息-不同POS条目模式组合")
    void testBuildAuthPrePostInfo_DifferentPosEntryModes() {
        // 准备数据
        when(sequenceIdGen.generateId(anyString())).thenReturn("123456");

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ApplicationManager> appManagerMock = mockStatic(ApplicationManager.class);
             MockedStatic<TimeZoneUtil> timeZoneUtilMock = mockStatic(TimeZoneUtil.class);
             MockedStatic<LocalDateTimeUtil> localDateTimeUtilMock = mockStatic(LocalDateTimeUtil.class);
             MockedStatic<CustAccountBO> custAccountBOMock = mockStatic(CustAccountBO.class)) {

            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            appManagerMock.when(() -> ApplicationManager.getTransTimeOfMastercard(any(), any()))
                    .thenReturn(LocalDateTime.now());
            timeZoneUtilMock.when(() -> TimeZoneUtil.timeZone(any(), any())).thenReturn(new BigDecimal("8.0"));
            localDateTimeUtilMock.when(() -> LocalDateTimeUtil.parse(anyString(), anyString()))
                    .thenReturn(LocalDateTime.now());
            custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);

            when(authPrePostLogService.insert(any(AuthPrePostLogDTO.class))).thenReturn(1);

            // 测试不同的POS条目模式组合
            String[][] testCases = {
                {"02", "0", "020"},
                {"90", "1", "901"},
                {"01", "2", "012"}
            };

            for (String[] testCase : testCases) {
                authRecordedDTO.setAuthServicePointCardCode(testCase[0]);
                authRecordedDTO.setAuthServicePointPinCode(testCase[1]);

                int result = authPrePostInfoServiceModifyImpl.buildAuthPrePostInfo(payload);
                assertEquals(1, result);

                AuthPrePostLogDTO resultDTO = payload.getAuthPrePostLogDTO();
                assertNotNull(resultDTO);
                assertEquals(testCase[2], resultDTO.getPosEntryMode());
            }

            // 测试空值情况
            authRecordedDTO.setAuthServicePointCardCode("");
            authRecordedDTO.setAuthServicePointPinCode("");

            int result = authPrePostInfoServiceModifyImpl.buildAuthPrePostInfo(payload);
            assertEquals(1, result);

            AuthPrePostLogDTO resultDTO = payload.getAuthPrePostLogDTO();
            assertNotNull(resultDTO);
            // 空值情况下，POS条目模式可能为空字符串或null，这是正常的
            assertTrue(resultDTO.getPosEntryMode() == null || resultDTO.getPosEntryMode().isEmpty() || "000".equals(resultDTO.getPosEntryMode()));
        }
    }

    @Test
    @DisplayName("构建授权预入账信息-手续费处理")
    void testBuildAuthPrePostInfo_WithAuthMarkupFee() {
        // 准备数据 - 使用AuthFeeDTO的实际属性
        when(sequenceIdGen.generateId(anyString())).thenReturn("123456");

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ApplicationManager> appManagerMock = mockStatic(ApplicationManager.class);
             MockedStatic<TimeZoneUtil> timeZoneUtilMock = mockStatic(TimeZoneUtil.class);
             MockedStatic<LocalDateTimeUtil> localDateTimeUtilMock = mockStatic(LocalDateTimeUtil.class);
             MockedStatic<CustAccountBO> custAccountBOMock = mockStatic(CustAccountBO.class)) {

            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            appManagerMock.when(() -> ApplicationManager.getTransTimeOfMastercard(any(), any()))
                    .thenReturn(LocalDateTime.now());
            timeZoneUtilMock.when(() -> TimeZoneUtil.timeZone(any(), any())).thenReturn(new BigDecimal("8.0"));
            localDateTimeUtilMock.when(() -> LocalDateTimeUtil.parse(anyString(), anyString()))
                    .thenReturn(LocalDateTime.now());
            custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);

            when(authPrePostLogService.insert(any(AuthPrePostLogDTO.class))).thenReturn(1);

            // 执行测试
            int result = authPrePostInfoServiceModifyImpl.buildAuthPrePostInfo(payload);

            // 验证结果
            assertEquals(1, result);

            AuthPrePostLogDTO resultDTO = payload.getAuthPrePostLogDTO();
            assertNotNull(resultDTO);
            // 验证基本字段设置正确
            assertEquals(authRecordedDTO.getAuthGlobalFlowNumber(), resultDTO.getAuthGlobalFlowNumber());
            assertEquals(authRecordedDTO.getAuthCardNumber(), resultDTO.getCardNumber());
        }
    }
}