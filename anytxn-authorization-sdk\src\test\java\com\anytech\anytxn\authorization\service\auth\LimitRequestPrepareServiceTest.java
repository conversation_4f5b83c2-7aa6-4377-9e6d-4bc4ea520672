package com.anytech.anytxn.authorization.service.auth;

import com.alibaba.fastjson.JSONArray;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.domain.dto.AuthPartnerReqDTO;
import com.anytech.anytxn.authorization.base.service.auth.IAuthCommonHandlerService;
import com.anytech.anytxn.authorization.base.service.auth.IAuthMatchRuleService;
import com.anytech.anytxn.authorization.base.service.auth.IOutstandingTransService;
import com.anytech.anytxn.authorization.base.service.partner.IPartnerMarginAuthService;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.CalculateLimitUnitDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.OutstandingTransactionDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.PreAuthorizationLogDTO;
import com.anytech.anytxn.business.base.authorization.enums.AuthResponseCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTransactionSourceCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.ReversalTypeEnum;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.card.domain.dto.MappingBusinessTokenDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CorporateCustomerInfoDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateCustomerInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateDownTopReferenceSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CorporateCustomerInfo;
import com.anytech.anytxn.business.dao.customer.model.CorporateDownTopReference;
import com.anytech.anytxn.business.base.limit.domain.dto.PartnerInfoDTO;
import com.anytech.anytxn.business.base.limit.domain.dto.PartnerMarginLogDTO;
import com.anytech.anytxn.business.base.limit.enums.PartnerAuthCheckClearIndEnum;
import com.anytech.anytxn.business.base.limit.enums.PartnerModeEnum;
import com.anytech.anytxn.business.dao.limit.mapper.PartnerInfoMapper;
import com.anytech.anytxn.business.dao.limit.model.PartnerInfo;
import com.anytech.anytxn.business.client.MappingFeign;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.limit.base.domain.bo.LimitCheckResultBO;
import com.anytech.anytxn.limit.base.domain.dto.CheckLimitReqDTO;
import com.anytech.anytxn.limit.base.domain.dto.LimitPreOccupiedReqDTO;
import com.anytech.anytxn.limit.base.domain.dto.payload.CalLimitTrialReqDTO;
import com.anytech.anytxn.limit.base.domain.dto.payload.CalLimitTrialResDTO;
import com.anytech.anytxn.limit.base.domain.dto.payload.CalLimitUnitReqDTO;
import com.anytech.anytxn.limit.base.domain.dto.payload.LimitReqDTO;
import com.anytech.anytxn.limit.service.CustomerLimitService;
import com.anytech.anytxn.limit.service.CustomerLimitUpdateService;
import com.anytech.anytxn.limit.service.LimitMatchCheckService;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.transaction.base.enums.DebitCreditIndicatorEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @description LimitRequestPrepareService的单元测试类
 * <AUTHOR>
 * @date 2025/07/08
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
class LimitRequestPrepareServiceTest {

    @Mock
    private LimitMatchCheckService limitMatchCheckService;

    @Mock
    private CustomerLimitUpdateService customerLimitUpdateService;

    @Mock
    private CustomerLimitService customerLimitService;

    @Mock
    private IAuthCommonHandlerService authCommonHandlerService;

    @Mock
    private IAuthMatchRuleService authMatchRuleService;

    @Mock
    private CorporateDownTopReferenceSelfMapper corporateDownTopReferenceSelfMapper;

    @Mock
    private CorporateCustomerInfoSelfMapper corporateCustomerInfoSelfMapper;

    @Mock
    private MappingFeign mappingFeign;

    @Mock
    private IPartnerMarginAuthService partnerMarginAuthService;

    @Mock
    private IOutstandingTransService outstandingTransService;

    @Mock
    private PartnerInfoMapper partnerInfoMapper;

    @Mock
    private SequenceIdGen sequenceIdGen;

    @InjectMocks
    private LimitRequestPrepareService limitRequestPrepareService;

    private AuthorizationCheckProcessingPayload payload;
    private AuthRecordedDTO authRecordedDTO;
    private CardAuthorizationDTO cardAuthorizationDTO;
    private OrganizationInfoResDTO orgInfo;
    private AccountManagementInfoDTO accountManagementInfoDTO;
    private CardProductInfoResDTO cardProductInfo;
    private CustomerAuthorizationInfoDTO customerAuthorizationInfoDTO;
    private LimitCheckResultBO limitCheckResultBO;
    private LimitPreOccupiedReqDTO limitPreOccupiedReqDTO;

    @BeforeEach
    void setUp() {
        // 直接Mock OrgNumberUtils静态字段，在测试中使用
        OrgNumberUtils.orgNumberUtil = mock(OrgNumberUtils.class);
        lenient().when(OrgNumberUtils.getOrg()).thenReturn("TEST_ORG");
        
        // 初始化测试数据 - 使用Mock对象避免BaseEntity构造函数问题
        authRecordedDTO = mock(AuthRecordedDTO.class);
        lenient().when(authRecordedDTO.getAuthCardNumber()).thenReturn("****************");
        lenient().when(authRecordedDTO.getAuthCardholderBillingAmount()).thenReturn(new BigDecimal("100.00"));
        lenient().when(authRecordedDTO.getAuthBillingCurrencyCode()).thenReturn("CNY");
        lenient().when(authRecordedDTO.getAuthGlobalFlowNumber()).thenReturn("GF123456789");
        lenient().when(authRecordedDTO.getAuthCustomerId()).thenReturn("CUST001");
        lenient().when(authRecordedDTO.getAuthTransactionTypeTopCode()).thenReturn("P");
        lenient().when(authRecordedDTO.getAuthTransactionTypeDetailCode()).thenReturn("P001");
        lenient().when(authRecordedDTO.getAuthTransactionCurrencyCode()).thenReturn("CNY");
        lenient().when(authRecordedDTO.getAuthTransactionAmount()).thenReturn(new BigDecimal("100.00"));
        lenient().when(authRecordedDTO.getCurrentAuthLogId()).thenReturn("LOG001");
        lenient().when(authRecordedDTO.getLimitUnitList()).thenReturn(new ArrayList<>());
        lenient().when(authRecordedDTO.getPartnerNotificationInd()).thenReturn(false);

        cardAuthorizationDTO = mock(CardAuthorizationDTO.class);
        lenient().when(cardAuthorizationDTO.getPrimaryCustomerId()).thenReturn("CUST001");
        lenient().when(cardAuthorizationDTO.getCorporateCustomerId()).thenReturn("CORP001");
        lenient().when(cardAuthorizationDTO.getPartnerId()).thenReturn("PARTNER001");
        lenient().when(cardAuthorizationDTO.getCardNumber()).thenReturn("****************");

        orgInfo = mock(OrganizationInfoResDTO.class);
        lenient().when(orgInfo.getOrganizationNumber()).thenReturn("ORG001");
        lenient().when(orgInfo.getNextProcessingDay()).thenReturn(LocalDate.now());

        accountManagementInfoDTO = mock(AccountManagementInfoDTO.class);
        lenient().when(accountManagementInfoDTO.getAccountManagementId()).thenReturn("ACC001");
        lenient().when(accountManagementInfoDTO.getProductNumber()).thenReturn("PROD001");

        cardProductInfo = mock(CardProductInfoResDTO.class);
        lenient().when(cardProductInfo.getAccountProductNumber()).thenReturn("ACCT_PROD001");
        lenient().when(cardProductInfo.getProductNumber()).thenReturn("PROD001");

        customerAuthorizationInfoDTO = mock(CustomerAuthorizationInfoDTO.class);
        lenient().when(customerAuthorizationInfoDTO.getGroupType()).thenReturn("INDIVIDUAL");

        payload = new AuthorizationCheckProcessingPayload();
        payload.setAuthRecordedDTO(authRecordedDTO);
        payload.setCardAuthorizationDTO(cardAuthorizationDTO);
        payload.setOrgInfo(orgInfo);
        payload.setAccountManagementInfoDTO(accountManagementInfoDTO);
        payload.setCardProductInfo(cardProductInfo);
        payload.setCustomerAuthorizationInfoDTO(customerAuthorizationInfoDTO);

        limitCheckResultBO = new LimitCheckResultBO();
        limitCheckResultBO.setCheckOver(true);

        limitPreOccupiedReqDTO = new LimitPreOccupiedReqDTO();
        limitPreOccupiedReqDTO.setDebitCreditIndicator(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());
        limitPreOccupiedReqDTO.setCtrlUnitDTOS(new ArrayList<>());
    }

    @Test
    @DisplayName("测试prepareLimitCheck方法 - 个人客户成功场景")
    void testPrepareLimitCheck_IndividualCustomer_Success() {
        // Given
        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authMatchRuleService.getLimitPreOccupied(any(), any(), any(), any())).thenReturn(limitPreOccupiedReqDTO);
        when(limitMatchCheckService.checkAvailableLimit(any(CheckLimitReqDTO.class))).thenReturn(limitCheckResultBO);

        // When
        LimitCheckResultBO result = limitRequestPrepareService.prepareLimitCheck(payload);

        // Then
        assertNotNull(result);
        assertTrue(result.getCheckOver());
        verify(limitMatchCheckService).checkAvailableLimit(any(CheckLimitReqDTO.class));
        verify(authCommonHandlerService, times(2)).authCardholderBillingAmount(any(), any());
        verify(authMatchRuleService).getLimitPreOccupied(any(), any(), any(), any());
    }

    @Test
    @DisplayName("测试prepareLimitCheck方法 - 公司客户成功场景")
    void testPrepareLimitCheck_CorporateCustomer_Success() {
        // Given
        // 通过设置公司客户ID来模拟公司客户场景
        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authMatchRuleService.getLimitPreOccupied(any(), any(), any(), any())).thenReturn(limitPreOccupiedReqDTO);
        when(limitMatchCheckService.checkAvailableLimit(any(CheckLimitReqDTO.class))).thenReturn(limitCheckResultBO);
        lenient().when(corporateDownTopReferenceSelfMapper.selectByParentIdOrChildId(anyString())).thenReturn(Collections.emptyList());

        // When
        LimitCheckResultBO result = limitRequestPrepareService.prepareLimitCheck(payload);

        // Then
        assertNotNull(result);
        assertTrue(result.getCheckOver());
        verify(limitMatchCheckService).checkAvailableLimit(any(CheckLimitReqDTO.class));
    }

    @Test
    @DisplayName("测试prepareLimitCheck方法 - 公司客户层级检查失败")
    void testPrepareLimitCheck_CorporateCustomer_HierarchyCheckFailed() {
        // Given
        LimitCheckResultBO failedResult = new LimitCheckResultBO();
        failedResult.setCheckOver(false);
        
        // 设置为公司客户
        when(cardAuthorizationDTO.isCorporateLiability()).thenReturn(true);
        when(cardAuthorizationDTO.getCorporateCustomerId()).thenReturn("CORP001");
        
        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authMatchRuleService.getLimitPreOccupied(any(), any(), any(), any())).thenReturn(limitPreOccupiedReqDTO);
        when(limitMatchCheckService.checkAvailableLimit(any(CheckLimitReqDTO.class)))
            .thenReturn(limitCheckResultBO)
            .thenReturn(failedResult);

        CorporateDownTopReference reference = new CorporateDownTopReference();
        reference.setCorporateParentId("PARENT001");
        when(corporateDownTopReferenceSelfMapper.selectByParentIdOrChildId(anyString())).thenReturn(Arrays.asList(reference));

        CorporateCustomerInfo parentInfo = new CorporateCustomerInfo();
        parentInfo.setCorporateCustomerId("PARENT001");
        parentInfo.setHierarchyLevel("1");
        when(corporateCustomerInfoSelfMapper.selectByCorpCusIdList(anyList())).thenReturn(Arrays.asList(parentInfo));

        // When
        LimitCheckResultBO result = limitRequestPrepareService.prepareLimitCheck(payload);

        // Then
        assertNotNull(result);
        assertFalse(result.getCheckOver());
        verify(limitMatchCheckService, times(2)).checkAvailableLimit(any(CheckLimitReqDTO.class));
    }

    @Test
    @DisplayName("测试prepareLimitCheck方法 - 空参数")
    void testPrepareLimitCheck_NullPayload() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            limitRequestPrepareService.prepareLimitCheck(null);
        });
    }

    @Test
    @DisplayName("测试updateLimit方法 - 成功场景")
    void testUpdateLimit_Success() {
        // Given
        CalculateLimitUnitDTO limitUnit = new CalculateLimitUnitDTO();
        limitUnit.setLimitUnitCode("UNIT001");
        when(authRecordedDTO.getLimitUnitList()).thenReturn(Arrays.asList(limitUnit));

        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());
        // calculateLimitAndUpdateForTxnRecored 方法可能有返回值，使用 doAnswer 替代 doNothing
        doAnswer(invocation -> null).when(customerLimitUpdateService).calculateLimitAndUpdateForTxnRecored(any());

        // When
        limitRequestPrepareService.updateLimit(payload);

        // Then
        verify(customerLimitUpdateService).calculateLimitAndUpdateForTxnRecored(any());
        verify(authCommonHandlerService).authCardholderBillingAmount(any(), any());
        verify(authCommonHandlerService).directionHandler(any());
    }

    @Test
    @DisplayName("测试updateLimit方法 - 包含合作伙伴保证金日志")
    void testUpdateLimit_WithPartnerMarginLog() {
        // Given
        CalculateLimitUnitDTO limitUnit = new CalculateLimitUnitDTO();
        when(authRecordedDTO.getLimitUnitList()).thenReturn(Arrays.asList(limitUnit));

        PartnerMarginLogDTO marginLog = new PartnerMarginLogDTO();
        marginLog.setUpdateInd(true);

        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());
        doAnswer(invocation -> {
            LimitReqDTO limitReqDTO = invocation.getArgument(0);
            limitReqDTO.setPartnerMarginLogDTO(marginLog);
            return null;
        }).when(customerLimitUpdateService).calculateLimitAndUpdateForTxnRecored(any());

        // When
        limitRequestPrepareService.updateLimit(payload);

        // Then
        verify(customerLimitUpdateService).calculateLimitAndUpdateForTxnRecored(any());
        // 验证保证金回滚日志处理逻辑
    }

    @Test
    @DisplayName("测试updateLimit方法 - 空参数")
    void testUpdateLimit_NullPayload() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            limitRequestPrepareService.updateLimit(null);
        });
    }

    @Test
    @DisplayName("测试recoverLimitForPreAuthComplete方法 - 成功场景")
    void testRecoverLimitForPreAuthComplete_Success() {
        // Given
        PreAuthorizationLogDTO preAuthLog = new PreAuthorizationLogDTO();
        preAuthLog.setLimitUnitJson("[{\"limitUnitCode\":\"UNIT001\",\"overdrawAmount\":100.00}]");

        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());
        doAnswer(invocation -> null).when(customerLimitUpdateService).calculateLimitAndUpdateForTxnRecored(any());

        // When
        limitRequestPrepareService.recoverLimitForPreAuthComplete(payload, preAuthLog);

        // Then
        verify(customerLimitUpdateService).calculateLimitAndUpdateForTxnRecored(any());
        verify(authCommonHandlerService).authCardholderBillingAmount(any(), any());
        verify(authCommonHandlerService).directionHandler(any());
    }

    @Test
    @DisplayName("测试recoverLimitForPreAuthComplete方法 - 空预授权日志")
    void testRecoverLimitForPreAuthComplete_NullPreAuthLog() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            limitRequestPrepareService.recoverLimitForPreAuthComplete(payload, null);
        });
    }

    @Test
    @DisplayName("测试limitUpdateForPreAuthCompleteCancel方法 - 成功场景")
    void testLimitUpdateForPreAuthCompleteCancel_Success() {
        // Given
        PreAuthorizationLogDTO originalTransLog = new PreAuthorizationLogDTO();
        CalculateLimitUnitDTO limitUnit = new CalculateLimitUnitDTO();
        when(authRecordedDTO.getLimitUnitList()).thenReturn(Arrays.asList(limitUnit));
        when(authRecordedDTO.getPostingTransactionCodeRev()).thenReturn(""); // 空字符串表示需要占用预授权完成金额

        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());
        doAnswer(invocation -> null).when(customerLimitUpdateService).calculateLimitAndUpdateForTxnRecored(any());

        // When
        limitRequestPrepareService.limitUpdateForPreAuthCompleteCancel(payload, originalTransLog);

        // Then
        verify(customerLimitUpdateService).calculateLimitAndUpdateForTxnRecored(any());
        verify(authCommonHandlerService).authCardholderBillingAmount(any(), any());
        verify(authCommonHandlerService).directionHandler(any());
    }

    @Test
    @DisplayName("测试limitUpdateForPreAuthCompleteCancel方法 - 有入账交易码")
    void testLimitUpdateForPreAuthCompleteCancel_WithPostingTransactionCode() {
        // Given
        PreAuthorizationLogDTO originalTransLog = new PreAuthorizationLogDTO();
        when(authRecordedDTO.getPostingTransactionCodeRev()).thenReturn("TXN001"); // 非空表示不需要占用预授权完成金额

        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());

        // When
        limitRequestPrepareService.limitUpdateForPreAuthCompleteCancel(payload, originalTransLog);

        // Then
        verify(customerLimitUpdateService, never()).calculateLimitAndUpdateForTxnRecored(any());
    }

    @Test
    @DisplayName("测试updateLimitForPreAuthComplete方法 - 成功场景")
    void testUpdateLimitForPreAuthComplete_Success() {
        // Given
        PreAuthorizationLogDTO preAuthLog = new PreAuthorizationLogDTO();
        preAuthLog.setLimitUnitJson("[{\"limitUnitCode\":\"UNIT001\",\"overdrawAmount\":100.00}]");
        when(authRecordedDTO.getLimitUnitVersion()).thenReturn("[{\"limitUnitCode\":\"UNIT001\",\"overdrawAmount\":100.00}]");
        when(authRecordedDTO.getPostingTransactionCodeRev()).thenReturn(""); // 空字符串表示需要占用预授权完成金额

        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());
        doAnswer(invocation -> null).when(customerLimitUpdateService).calculateLimitAndUpdateForTxnRecored(any());

        // When
        limitRequestPrepareService.updateLimitForPreAuthComplete(payload, preAuthLog);

        // Then
        verify(customerLimitUpdateService, times(2)).calculateLimitAndUpdateForTxnRecored(any());
        verify(authCommonHandlerService, times(1)).authCardholderBillingAmount(any(), any());
        verify(authCommonHandlerService, times(1)).directionHandler(any());
    }

    @Test
    @DisplayName("测试updateLimitForPreAuthComplete方法 - 有入账交易码")
    void testUpdateLimitForPreAuthComplete_WithPostingTransactionCode() {
        // Given
        PreAuthorizationLogDTO preAuthLog = new PreAuthorizationLogDTO();
        preAuthLog.setLimitUnitJson("[{\"limitUnitCode\":\"UNIT001\",\"overdrawAmount\":100.00}]");
        when(authRecordedDTO.getPostingTransactionCodeRev()).thenReturn("TXN001"); // 非空表示不需要占用预授权完成金额

        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());
        doAnswer(invocation -> null).when(customerLimitUpdateService).calculateLimitAndUpdateForTxnRecored(any());

        // When
        limitRequestPrepareService.updateLimitForPreAuthComplete(payload, preAuthLog);

        // Then
        verify(customerLimitUpdateService, times(1)).calculateLimitAndUpdateForTxnRecored(any()); // 只调用一次
    }

    @Test
    @DisplayName("测试commonBuild方法 - 基础版本成功")
    void testCommonBuild_BasicVersion_Success() {
        // Given
        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("TEST_ORG");

            // When
            LimitReqDTO result = limitRequestPrepareService.commonBuild(payload);

            // Then
            assertNotNull(result);
            assertEquals("ORG001", result.getOrganizationNumber());
            assertEquals("CUST001", result.getCustomerId());
            assertEquals("GF123456789", result.getGlobalFlowNumber());
            assertEquals("ACC001", result.getAccountManagementId());
            assertEquals("****************", result.getCardNumber());
            assertEquals(new BigDecimal("100.00"), result.getTransactionAmount());
            assertEquals(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(), result.getDebitCreditIndicator());
            assertEquals("CNY", result.getAccountCurrency());
            assertEquals("INDIVIDUAL", result.getGroupType());
            assertEquals("PROD001", result.getAccountProductCode());
        }
    }

    @Test
    @DisplayName("测试commonBuild方法 - 公司客户场景")
    void testCommonBuild_CorporateCustomer() {
        // Given
        // 通过模拟isCorporateLiability()返回true来测试公司客户场景
        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("TEST_ORG");

            // When
            LimitReqDTO result = limitRequestPrepareService.commonBuild(payload);

            // Then
            assertNotNull(result);
            // 在实际的业务逻辑中，会根据isCorporateLiability()判断使用哪个客户ID
            assertEquals("CUST001", result.getCustomerId());
        }
    }

    @Test
    @DisplayName("测试commonBuild方法 - 带合作伙伴通知参数版本")
    void testCommonBuild_WithPartnerNotifyInd() {
        // Given
        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("TEST_ORG");

            // When
            LimitReqDTO result = limitRequestPrepareService.commonBuild(payload, true);

            // Then
            assertNotNull(result);
            assertEquals("ORG001", result.getOrganizationNumber());
            assertEquals("CUST001", result.getCustomerId());
        }
    }

    @Test
    @DisplayName("测试partnerAuthNotify方法 - 成功场景")
    void testPartnerAuthNotify_Success() {
        // Given
        PartnerInfoDTO partnerInfoDTO = new PartnerInfoDTO();
        partnerInfoDTO.setPartnerId("PARTNER001");
        partnerInfoDTO.setAuthCheckClearInd(PartnerAuthCheckClearIndEnum.CLEAR.getCode());
        
        LimitReqDTO limitReqDTO = new LimitReqDTO();
        PartnerMarginLogDTO marginLog = new PartnerMarginLogDTO();
        marginLog.setRequestAmount(new BigDecimal("100.00"));
        limitReqDTO.setPartnerMarginLogDTO(marginLog);

        when(authRecordedDTO.getAuthTransmissionTime()).thenReturn("0708101030");
        when(authRecordedDTO.getAuthLocalTransactionDate()).thenReturn("0708");
        when(authRecordedDTO.getAuthLocalTransactionTime()).thenReturn("101030");
        when(authRecordedDTO.getAuthTransactionSourceCode()).thenReturn(AuthTransactionSourceCodeEnum.ONUS.getCode());

        when(partnerMarginAuthService.partnerMarginAuthCall(any(), any())).thenReturn("00");

        // When
        limitRequestPrepareService.partnerAuthNotify(payload, limitReqDTO, partnerInfoDTO);

        // Then
        verify(partnerMarginAuthService).partnerMarginAuthCall(any(), any());
        assertTrue(authRecordedDTO.getPartnerNotificationInd());
    }

    @Test
    @DisplayName("测试partnerAuthNotify方法 - 请求金额为零")
    void testPartnerAuthNotify_ZeroRequestAmount() {
        // Given
        PartnerInfoDTO partnerInfoDTO = new PartnerInfoDTO();
        partnerInfoDTO.setPartnerId("PARTNER001");
        partnerInfoDTO.setAuthCheckClearInd(PartnerAuthCheckClearIndEnum.CLEAR.getCode());
        
        LimitReqDTO limitReqDTO = new LimitReqDTO();
        PartnerMarginLogDTO marginLog = new PartnerMarginLogDTO();
        marginLog.setRequestAmount(BigDecimal.ZERO);
        limitReqDTO.setPartnerMarginLogDTO(marginLog);

        lenient().when(authRecordedDTO.getAuthTransmissionTime()).thenReturn("0708101030");

        // When
        limitRequestPrepareService.partnerAuthNotify(payload, limitReqDTO, partnerInfoDTO);

        // Then
        verify(partnerMarginAuthService, never()).partnerMarginAuthCall(any(), any());
        assertFalse(authRecordedDTO.getPartnerNotificationInd());
    }

    @Test
    @DisplayName("测试partnerAuthNotify方法 - Token未找到")
    void testPartnerAuthNotify_TokenNotFound() {
        // Given
        PartnerInfoDTO partnerInfoDTO = new PartnerInfoDTO();
        partnerInfoDTO.setPartnerId("PARTNER001");
        partnerInfoDTO.setAuthCheckClearInd(PartnerAuthCheckClearIndEnum.CLEAR.getCode());
        
        LimitReqDTO limitReqDTO = new LimitReqDTO();
        PartnerMarginLogDTO marginLog = new PartnerMarginLogDTO();
        marginLog.setRequestAmount(new BigDecimal("100.00"));
        limitReqDTO.setPartnerMarginLogDTO(marginLog);

        when(authRecordedDTO.getAuthTransmissionTime()).thenReturn("0708101030");
        lenient().when(authRecordedDTO.getPartnerRejectInd()).thenReturn(false).thenReturn(true);

        when(partnerMarginAuthService.partnerMarginAuthCall(any(), any())).thenReturn("51");

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () -> {
            limitRequestPrepareService.partnerAuthNotify(payload, limitReqDTO, partnerInfoDTO);
        });
        assertEquals(AuthResponseCodeEnum.PARTNER_REJECT.getCode(), exception.getErrCode());
        verify(authRecordedDTO).setPartnerRejectInd(true);
    }

    @Test
    @DisplayName("测试partnerAuthNotify方法 - 合作伙伴拒绝")
    void testPartnerAuthNotify_PartnerReject() {
        // Given
        PartnerInfoDTO partnerInfoDTO = new PartnerInfoDTO();
        partnerInfoDTO.setPartnerId("PARTNER001");
        partnerInfoDTO.setAuthCheckClearInd(PartnerAuthCheckClearIndEnum.CLEAR.getCode());
        
        LimitReqDTO limitReqDTO = new LimitReqDTO();
        PartnerMarginLogDTO marginLog = new PartnerMarginLogDTO();
        marginLog.setRequestAmount(new BigDecimal("100.00"));
        limitReqDTO.setPartnerMarginLogDTO(marginLog);

        when(authRecordedDTO.getAuthTransmissionTime()).thenReturn("0708101030");
        lenient().when(authRecordedDTO.getPartnerRejectInd()).thenReturn(false).thenReturn(true);

        when(partnerMarginAuthService.partnerMarginAuthCall(any(), any())).thenReturn("05");

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () -> {
            limitRequestPrepareService.partnerAuthNotify(payload, limitReqDTO, partnerInfoDTO);
        });
        assertEquals(AuthResponseCodeEnum.PARTNER_REJECT.getCode(), exception.getErrCode());
        verify(authRecordedDTO).setPartnerRejectInd(true);
    }

    @Test
    @DisplayName("测试partnerAuthNotify方法 - 合作伙伴超时")
    void testPartnerAuthNotify_PartnerTimeout() {
        // Given
        PartnerInfoDTO partnerInfoDTO = new PartnerInfoDTO();
        partnerInfoDTO.setPartnerId("PARTNER001");
        partnerInfoDTO.setAuthCheckClearInd(PartnerAuthCheckClearIndEnum.CLEAR.getCode());
        
        LimitReqDTO limitReqDTO = new LimitReqDTO();
        PartnerMarginLogDTO marginLog = new PartnerMarginLogDTO();
        marginLog.setRequestAmount(new BigDecimal("100.00"));
        limitReqDTO.setPartnerMarginLogDTO(marginLog);

        when(authRecordedDTO.getAuthTransmissionTime()).thenReturn("0708101030");
        lenient().when(authRecordedDTO.getPartnerRejectInd()).thenReturn(false).thenReturn(true);

        when(partnerMarginAuthService.partnerMarginAuthCall(any(), any())).thenReturn("TIMEOUT");
        lenient().doNothing().when(partnerMarginAuthService).partnerMarginTimeoutLog(any(), any(), any(), any());

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () -> {
            limitRequestPrepareService.partnerAuthNotify(payload, limitReqDTO, partnerInfoDTO);
        });
        assertEquals(AuthResponseCodeEnum.PARTNER_REJECT.getCode(), exception.getErrCode());
        verify(authRecordedDTO).setPartnerRejectInd(true);
        verify(partnerMarginAuthService).partnerMarginAuthCall(any(), any());
    }

    @Test
    @DisplayName("测试partnerAuthNotify方法 - 反向交易场景")
    void testPartnerAuthNotify_ReversalTransaction() {
        // Given
        PartnerInfoDTO partnerInfoDTO = new PartnerInfoDTO();
        partnerInfoDTO.setPartnerId("PARTNER001");
        partnerInfoDTO.setAuthCheckClearInd(PartnerAuthCheckClearIndEnum.CLEAR.getCode());
        
        LimitReqDTO limitReqDTO = new LimitReqDTO();
        limitReqDTO.setDebitCreditIndicator(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode());
        PartnerMarginLogDTO marginLog = new PartnerMarginLogDTO();
        marginLog.setRequestAmount(new BigDecimal("100.00"));
        limitReqDTO.setPartnerMarginLogDTO(marginLog);

        when(authRecordedDTO.getAuthTransmissionTime()).thenReturn("0708101030");
        when(authRecordedDTO.getAuthReversalType()).thenReturn(ReversalTypeEnum.REVERSAL_TRANS.getCode());

        when(partnerMarginAuthService.partnerMarginAuthCall(any(), any())).thenReturn("00");

        // When
        limitRequestPrepareService.partnerAuthNotify(payload, limitReqDTO, partnerInfoDTO);

        // Then
        verify(partnerMarginAuthService).partnerMarginAuthCall(any(), any());
        assertTrue(authRecordedDTO.getPartnerNotificationInd());
    }

    @Test
    @DisplayName("测试getLimitAvailable方法 - 成功场景")
    void testGetLimitAvailable_Success() {
        // Given
        CalLimitTrialResDTO expectedResult = new CalLimitTrialResDTO();

        when(authCommonHandlerService.visaHandler(any())).thenReturn("VISA_TXN");
        when(customerLimitService.tryToCalculateLimit(any(CalLimitTrialReqDTO.class))).thenReturn(expectedResult);

        // When
        CalLimitTrialResDTO result = limitRequestPrepareService.getLimitAvailable(payload);

        // Then
        assertNotNull(result);
        verify(customerLimitService).tryToCalculateLimit(any(CalLimitTrialReqDTO.class));
        verify(authCommonHandlerService).visaHandler(any());
    }

    @Test
    @DisplayName("测试getLimitAvailable方法 - 空参数")
    void testGetLimitAvailable_NullPayload() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            limitRequestPrepareService.getLimitAvailable(null);
        });
    }

    @Test
    @DisplayName("测试边界条件 - 空的限额单元列表")
    void testBoundaryCondition_EmptyLimitUnitList() {
        // Given
        when(authRecordedDTO.getLimitUnitList()).thenReturn(Collections.emptyList());

        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());
        doAnswer(invocation -> null).when(customerLimitUpdateService).calculateLimitAndUpdateForTxnRecored(any());

        // When
        limitRequestPrepareService.updateLimit(payload);

        // Then
        verify(customerLimitUpdateService).calculateLimitAndUpdateForTxnRecored(any());
    }

    @Test
    @DisplayName("测试边界条件 - 空的合作伙伴ID")
    void testBoundaryCondition_EmptyPartnerId() {
        // Given
        when(cardAuthorizationDTO.getPartnerId()).thenReturn("");

        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());

        // When
        LimitReqDTO result = limitRequestPrepareService.commonBuild(payload);

        // Then
        assertNotNull(result);
        verify(partnerInfoMapper, never()).selectByPartnerId(anyString());
    }

    @Test
    @DisplayName("测试异常处理 - 数据库操作异常")
    void testExceptionHandling_DatabaseException() {
        // Given
        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authMatchRuleService.getLimitPreOccupied(any(), any(), any(), any())).thenReturn(limitPreOccupiedReqDTO);
        when(limitMatchCheckService.checkAvailableLimit(any(CheckLimitReqDTO.class)))
            .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            limitRequestPrepareService.prepareLimitCheck(payload);
        });
    }

    @Test
    @DisplayName("测试commonBuild方法 - 包含合作伙伴检查")
    void testCommonBuild_WithPartnerCheck() {
        // Given
        PartnerInfo partnerInfo = new PartnerInfo();
        partnerInfo.setPartnerId("PARTNER001");
        partnerInfo.setPartnerMode(PartnerModeEnum.PA.getCode());
        
        PartnerInfoDTO partnerInfoDTO = new PartnerInfoDTO();
        partnerInfoDTO.setOverPaymentTransInd("Y");
        
        when(authCommonHandlerService.authCardholderBillingAmount(any(), any())).thenReturn(new BigDecimal("100.00"));
        when(authCommonHandlerService.directionHandler(any())).thenReturn(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode());
        when(partnerInfoMapper.selectByPartnerId("PARTNER001")).thenReturn(partnerInfo);
        when(sequenceIdGen.generateId(any())).thenReturn("SEQ001");
        when(authRecordedDTO.getPartnerInfoDTO()).thenReturn(partnerInfoDTO);
        when(authRecordedDTO.getAuthTransmissionTime()).thenReturn("0708101030");
        when(authRecordedDTO.getPartnerNotificationInd()).thenReturn(false);

        // Mock partnerMarginAuthService to return a successful response
        when(partnerMarginAuthService.partnerMarginAuthCall(any(), any())).thenReturn("00");

        // 设置必要的属性，避免空指针异常
        PartnerMarginLogDTO marginLog = new PartnerMarginLogDTO();
        marginLog.setRequestAmount(new BigDecimal("100.00"));
        when(authRecordedDTO.getPartnerMarginLogDTO()).thenReturn(marginLog);

        // Mock mappingFeign response
        MappingBusinessTokenDTO mockTokenDTO = new MappingBusinessTokenDTO();
        mockTokenDTO.setCardToken("CARD_TOKEN_123");
        mockTokenDTO.setCustomerToken("CUSTOMER_TOKEN_123");

        @SuppressWarnings("unchecked")
        AnyTxnHttpResponse<MappingBusinessTokenDTO> mockTokenResponse = mock(AnyTxnHttpResponse.class);
        when(mockTokenResponse.getData()).thenReturn(mockTokenDTO);
        when(mappingFeign.getBusinessTokenByCardNumber(anyString())).thenReturn(mockTokenResponse);

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("TEST_ORG");

            // When
            LimitReqDTO result = limitRequestPrepareService.commonBuild(payload, true);

            // Then
            assertNotNull(result);
            verify(partnerInfoMapper).selectByPartnerId("PARTNER001");
        }
    }
} 