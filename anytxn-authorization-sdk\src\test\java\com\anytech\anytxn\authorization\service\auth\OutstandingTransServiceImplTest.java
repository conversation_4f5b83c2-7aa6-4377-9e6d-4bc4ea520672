package com.anytech.anytxn.authorization.service.auth;

import com.anytech.anytxn.authorization.base.domain.bo.LimitLogBO;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.base.service.auth.IAuthPrePostLogService;
import com.anytech.anytxn.business.base.authorization.domain.dto.OutstandingTransactionDTO;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthRepDetailEnum;
import com.anytech.anytxn.business.dao.authorization.mapper.OutstandingTransactionHistoryMapper;
import com.anytech.anytxn.business.dao.authorization.mapper.OutstandingTransactionMapper;
import com.anytech.anytxn.business.dao.authorization.mapper.OutstandingTransactionSelfMapper;
import com.anytech.anytxn.business.dao.authorization.model.OutstandingTransaction;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.limit.mapper.PartnerInfoMapper;
import com.anytech.anytxn.business.dao.limit.model.PartnerInfo;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.enums.CorporateIndicatorEnum;
import com.anytech.anytxn.common.core.enums.LiabilityEnum;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.limit.service.CustomerLimitUpdateService;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDTO;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmSysDict;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCtrlUnitService;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmSysDictSelfMapper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @description OutstandingTransServiceImpl的单元测试类
 * <AUTHOR>
 * @date 2025/07/08
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("未并账服务实现类测试")
class OutstandingTransServiceImplTest {

    @InjectMocks
    private OutstandingTransServiceImpl outstandingTransService;

    @Mock
    private OutstandingTransactionSelfMapper outstandingTransactionSelfMapper;

    @Mock
    private OutstandingTransactionMapper outstandingTransactionMapper;

    @Mock
    private CustomerLimitUpdateService customerLimitUpdateService;

    @Mock
    private ParmSysDictSelfMapper parmSysDictSelfMapper;

    @Mock
    private ITransactionCodeService transactionCodeService;

    @Mock
    private OutstandingTransactionHistoryMapper outstandingTransactionHistoryMapper;

    @Mock
    private ITransactionCtrlUnitService transactionCtrlUnitService;

    @Mock
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;

    @Mock
    private PartnerInfoMapper partnerInfoMapper;

    @Mock
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;

    @Mock
    private SequenceIdGen sequenceIdGen;

    @Mock
    private IAuthPrePostLogService authPrePostLogService;

    private OutstandingTransaction testOutstandingTransaction;
    private OutstandingTransactionDTO testOutstandingTransactionDTO;
    private CardAuthorizationInfo testCardAuthorizationInfo;
    private PartnerInfo testPartnerInfo;
    private ParmOrganizationInfo testParmOrganizationInfo;

    @BeforeEach
    void setUp() {
        // 直接Mock OrgNumberUtils静态字段
        OrgNumberUtils.orgNumberUtil = mock(OrgNumberUtils.class);
        lenient().when(OrgNumberUtils.getOrg()).thenReturn("001");
        
        // 初始化测试数据
        testOutstandingTransaction = new OutstandingTransaction();
        testOutstandingTransaction.setOutstandingTransactionId("TEST_ID_001");
        testOutstandingTransaction.setGlobalFlowNumber("GLOBAL_001");
        testOutstandingTransaction.setCardNumber("1234567890123456");
        testOutstandingTransaction.setOrganizationNumber("ORG_001");
        testOutstandingTransaction.setTransactionAmount(BigDecimal.valueOf(100.00));
        testOutstandingTransaction.setLimitUnitJson("[{\"limitUnitCode\":\"UNIT_001\",\"amount\":100.00}]");
        testOutstandingTransaction.setCustomerId("123456789");
        testOutstandingTransaction.setVersionNumber(1L);

        testOutstandingTransactionDTO = new OutstandingTransactionDTO();
        testOutstandingTransactionDTO.setOutstandingTransactionId("TEST_ID_001");
        testOutstandingTransactionDTO.setGlobalFlowNumber("GLOBAL_001");
        testOutstandingTransactionDTO.setCardNumber("1234567890123456");
        testOutstandingTransactionDTO.setOrganizationNumber("ORG_001");
        testOutstandingTransactionDTO.setTransactionAmount(BigDecimal.valueOf(100.00));
        testOutstandingTransactionDTO.setLimitUnitJson("[{\"limitUnitCode\":\"UNIT_001\",\"amount\":100.00}]");
        testOutstandingTransactionDTO.setCustomerId("123456789");
        testOutstandingTransactionDTO.setVersionNumber(1L);

        testCardAuthorizationInfo = new CardAuthorizationInfo();
        testCardAuthorizationInfo.setCardNumber("1234567890123456");
        testCardAuthorizationInfo.setCorporateIndicator(CorporateIndicatorEnum.PERSONAL.getCode());
        testCardAuthorizationInfo.setLiability(LiabilityEnum.PERSONAL.getCode());
        testCardAuthorizationInfo.setPartnerId("PARTNER_001");

        testPartnerInfo = new PartnerInfo();
        testPartnerInfo.setPartnerId("PARTNER_001");

        testParmOrganizationInfo = new ParmOrganizationInfo();
        testParmOrganizationInfo.setToday(LocalDate.now());
    }

    @Test
    @DisplayName("根据管理账户ID和全局流水号查询未并账信息-成功")
    void testGetInfoByAccountManageIdAndGlobalFlowNumber_Success() {
        // Given
        String accountManageId = "ACCOUNT_001";
        String globalFlowNumber = "GLOBAL_001";
        when(outstandingTransactionSelfMapper.selectByAccountManageIdAndGlobalFlowNumber(accountManageId, globalFlowNumber))
                .thenReturn(testOutstandingTransaction);

        // When
        OutstandingTransactionDTO result = outstandingTransService.getInfoByAccountManageIdAndGlobalFlowNumber(accountManageId, globalFlowNumber);

        // Then
        assertNotNull(result);
        assertEquals(testOutstandingTransaction.getOutstandingTransactionId(), result.getOutstandingTransactionId());
        assertEquals(testOutstandingTransaction.getGlobalFlowNumber(), result.getGlobalFlowNumber());
        verify(outstandingTransactionSelfMapper).selectByAccountManageIdAndGlobalFlowNumber(accountManageId, globalFlowNumber);
    }

    @Test
    @DisplayName("根据管理账户ID和全局流水号查询未并账信息-未找到数据")
    void testGetInfoByAccountManageIdAndGlobalFlowNumber_NotFound() {
        // Given
        String accountManageId = "ACCOUNT_001";
        String globalFlowNumber = "GLOBAL_001";
        when(outstandingTransactionSelfMapper.selectByAccountManageIdAndGlobalFlowNumber(accountManageId, globalFlowNumber))
                .thenReturn(null);

        // When
        OutstandingTransactionDTO result = outstandingTransService.getInfoByAccountManageIdAndGlobalFlowNumber(accountManageId, globalFlowNumber);

        // Then
        assertNull(result);
        verify(outstandingTransactionSelfMapper).selectByAccountManageIdAndGlobalFlowNumber(accountManageId, globalFlowNumber);
    }

    @Test
    @DisplayName("根据管理账户ID和全局流水号查询未并账信息-数据库异常")
    void testGetInfoByAccountManageIdAndGlobalFlowNumber_DatabaseException() {
        // Given
        String accountManageId = "ACCOUNT_001";
        String globalFlowNumber = "GLOBAL_001";
        when(outstandingTransactionSelfMapper.selectByAccountManageIdAndGlobalFlowNumber(accountManageId, globalFlowNumber))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.getInfoByAccountManageIdAndGlobalFlowNumber(accountManageId, globalFlowNumber));
        assertEquals(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR.getCode(), exception.getErrCode());
        verify(outstandingTransactionSelfMapper).selectByAccountManageIdAndGlobalFlowNumber(accountManageId, globalFlowNumber);
    }

    @Test
    @DisplayName("根据管理账户ID、全局流水号和记账标志查询未并账信息-成功")
    void testGetInfoByAccountManageIdAndGlobalFlowNumberAndPostFlag_Success() {
        // Given
        String accountManageId = "ACCOUNT_001";
        String globalFlowNumber = "GLOBAL_001";
        String postFlag = "Y";
        when(outstandingTransactionSelfMapper.getInfoByAccountManageIdAndGlobalFlowNumberAndPostFlag(accountManageId, globalFlowNumber, postFlag))
                .thenReturn(testOutstandingTransaction);

        // When
        OutstandingTransactionDTO result = outstandingTransService.getInfoByAccountManageIdAndGlobalFlowNumberAndPostFlag(accountManageId, globalFlowNumber, postFlag);

        // Then
        assertNotNull(result);
        assertEquals(testOutstandingTransaction.getOutstandingTransactionId(), result.getOutstandingTransactionId());
        verify(outstandingTransactionSelfMapper).getInfoByAccountManageIdAndGlobalFlowNumberAndPostFlag(accountManageId, globalFlowNumber, postFlag);
    }

    @Test
    @DisplayName("根据管理账户ID、全局流水号和记账标志查询未并账信息-未找到数据")
    void testGetInfoByAccountManageIdAndGlobalFlowNumberAndPostFlag_NotFound() {
        // Given
        String accountManageId = "ACCOUNT_001";
        String globalFlowNumber = "GLOBAL_001";
        String postFlag = "Y";
        when(outstandingTransactionSelfMapper.getInfoByAccountManageIdAndGlobalFlowNumberAndPostFlag(accountManageId, globalFlowNumber, postFlag))
                .thenReturn(null);

        // When
        OutstandingTransactionDTO result = outstandingTransService.getInfoByAccountManageIdAndGlobalFlowNumberAndPostFlag(accountManageId, globalFlowNumber, postFlag);

        // Then
        assertNull(result);
        verify(outstandingTransactionSelfMapper).getInfoByAccountManageIdAndGlobalFlowNumberAndPostFlag(accountManageId, globalFlowNumber, postFlag);
    }

    @Test
    @DisplayName("根据ID删除未并账信息-成功")
    void testCancelByOutstandingTransactionId_Success() {
        // Given
        String outstandingTransactionId = "TEST_ID_001";
        when(outstandingTransactionMapper.deleteByPrimaryKey(outstandingTransactionId)).thenReturn(1);

        // When
        Boolean result = outstandingTransService.cancelByOutstandingTransactionId(outstandingTransactionId);

        // Then
        assertTrue(result);
        verify(outstandingTransactionMapper).deleteByPrimaryKey(outstandingTransactionId);
    }

    @Test
    @DisplayName("根据ID删除未并账信息-参数为空")
    void testCancelByOutstandingTransactionId_NullParam() {
        // Given
        String outstandingTransactionId = null;

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.cancelByOutstandingTransactionId(outstandingTransactionId));
        assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL.getCode(), exception.getErrCode());
        assertEquals("The incoming parameter is null:Delete unconsolidated information ID is null", exception.getErrDetail());
    }

    @Test
    @DisplayName("根据ID删除未并账信息-数据不存在")
    void testCancelByOutstandingTransactionId_DataNotExists() {
        // Given
        String outstandingTransactionId = "TEST_ID_001";
        when(outstandingTransactionMapper.deleteByPrimaryKey(outstandingTransactionId)).thenReturn(0);

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.cancelByOutstandingTransactionId(outstandingTransactionId));
        assertEquals("1213030020", exception.getErrCode());
        assertEquals("Database operation exception", exception.getErrDetail());
    }

    @Test
    @DisplayName("根据ID删除未并账信息-数据库异常")
    void testCancelByOutstandingTransactionId_DatabaseException() {
        // Given
        String outstandingTransactionId = "TEST_ID_001";
        when(outstandingTransactionMapper.deleteByPrimaryKey(outstandingTransactionId))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.cancelByOutstandingTransactionId(outstandingTransactionId));
        assertEquals(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR.getCode(), exception.getErrCode());
        verify(outstandingTransactionMapper).deleteByPrimaryKey(outstandingTransactionId);
    }

    @Test
    @DisplayName("批量处理未并账信息-空限额单元JSON")
    void testBatchProcess_EmptyLimitUnitJson() {
        // Given
        OutstandingTransactionDTO outstandingTransaction = new OutstandingTransactionDTO();
        outstandingTransaction.setLimitUnitJson("");

        // When
        LimitLogBO result = outstandingTransService.batchPorcess(outstandingTransaction);

        // Then
        assertNotNull(result);
        assertEquals(outstandingTransaction, result.getOutstandingTransactionDTO());
    }

    @Test
    @DisplayName("批量处理未并账信息-有限额单元JSON")
    void testBatchProcess_WithLimitUnitJson() {
        // Given
        OutstandingTransactionDTO outstandingTransaction = new OutstandingTransactionDTO();
        outstandingTransaction.setLimitUnitJson("[{\"limitUnitCode\":\"UNIT_001\",\"amount\":100.00}]");
        outstandingTransaction.setOrganizationNumber("ORG_001");
        outstandingTransaction.setCustomerId("CUSTOMER_001");
        outstandingTransaction.setCardNumber("1234567890123456");
        outstandingTransaction.setGlobalFlowNumber("GLOBAL_001");
        outstandingTransaction.setTransactionAmount(BigDecimal.valueOf(100.00));
        outstandingTransaction.setDebitCreditIndcator("D");
        outstandingTransaction.setPostingTransactionCode("001");

        CardAuthorizationInfo cardInfo = new CardAuthorizationInfo();
        cardInfo.setCorporateIndicator(CorporateIndicatorEnum.PERSONAL.getCode());
        cardInfo.setLiability(LiabilityEnum.PERSONAL.getCode());

        TransactionCodeResDTO transactionCodeInfo = new TransactionCodeResDTO();
        transactionCodeInfo.setDebitCreditIndicator("C");

        when(cardAuthorizationInfoMapper.selectByPrimaryKey(anyString(), anyString())).thenReturn(cardInfo);
        when(transactionCodeService.findTransactionCode(anyString(), anyString())).thenReturn(transactionCodeInfo);

        // When
        LimitLogBO result = outstandingTransService.batchPorcess(outstandingTransaction);

        // Then
        assertNotNull(result);
        assertNotNull(result.getOutstandingTransactionDTO());
        assertNotNull(result.getLimitReqDTO());
        assertEquals(outstandingTransaction, result.getOutstandingTransactionDTO());
    }

    @Test
    @DisplayName("批量写入处理-成功")
    void testBatchWriter_Success() {
        // Given
        List<LimitLogBO> items = new ArrayList<>();
        LimitLogBO limitLogBO = new LimitLogBO();
        limitLogBO.setOutstandingTransactionDTO(testOutstandingTransactionDTO);
        
        // 为LimitLogBO设置AuthPrePostLogDTO
        com.anytech.anytxn.business.base.authorization.domain.dto.AuthPrePostLogDTO authPrePostLogDTO = 
            new com.anytech.anytxn.business.base.authorization.domain.dto.AuthPrePostLogDTO();
        authPrePostLogDTO.setGlobalFlowNumber("GLOBAL_001");
        authPrePostLogDTO.setOrganizationNumber("ORG_001");
        limitLogBO.setAuthPrePostLogDTO(authPrePostLogDTO);
        
        items.add(limitLogBO);

        when(outstandingTransactionMapper.updateByPrimaryKeySelective(any(OutstandingTransaction.class))).thenReturn(1);
        when(sequenceIdGen.generateId(any())).thenReturn("SEQ_001");
        when(authPrePostLogService.insert(any())).thenReturn(1);

        // When
        assertDoesNotThrow(() -> outstandingTransService.batchWriter(items));

        // Then
        verify(outstandingTransactionMapper).updateByPrimaryKeySelective(any(OutstandingTransaction.class));
        verify(authPrePostLogService).insert(any());
    }

    @Test
    @DisplayName("获取计数-成功")
    void testGetCount_Success() {
        // Given
        String partitionKey = "KEY1-KEY2";
        List<Map<String, Object>> orgs = new ArrayList<>();
        when(outstandingTransactionSelfMapper.getCount(anyString(), anyString(), anyList())).thenReturn(5);

        // When
        int result = outstandingTransService.getCount(partitionKey, orgs);

        // Then
        assertEquals(5, result);
        verify(outstandingTransactionSelfMapper).getCount("KEY1", "KEY2", orgs);
    }

    @Test
    @DisplayName("查询未并账交易ID列表-成功")
    void testQueryOutstandingTransactionIds_Success() {
        // Given
        String partitionKey = "KEY1-KEY2";
        List<Map<String, Object>> orgs = new ArrayList<>();
        ArrayList<Integer> rowNumbers = new ArrayList<>();
        List<String> expectedIds = Arrays.asList("ID1", "ID2", "ID3");
        when(outstandingTransactionSelfMapper.selectOutstandingTransactionIds(anyString(), anyString(), anyList(), any(ArrayList.class)))
                .thenReturn(expectedIds);

        // When
        List<String> result = outstandingTransService.queryOutstandingTransactionIds(partitionKey, orgs, rowNumbers);

        // Then
        assertEquals(expectedIds, result);
        verify(outstandingTransactionSelfMapper).selectOutstandingTransactionIds("KEY1", "KEY2", orgs, rowNumbers);
    }

    @Test
    @DisplayName("分页查询未并账交易列表-成功")
    void testQueryOutStandingTransactionList_Success() {
        // Given
        Integer page = 1;
        Integer rows = 10;
        String cardNumber = "1234567890123456";
        String organizationNumber = "ORG_001";

        List<OutstandingTransaction> transactionList = Arrays.asList(testOutstandingTransaction);
        List<ParmSysDict> dictList = new ArrayList<>();
        ParmSysDict dict = new ParmSysDict();
        dict.setCodeId("001");
        dict.setCodeName("测试交易");
        dictList.add(dict);

        when(outstandingTransactionSelfMapper.selectByCardNumberAndOrg(cardNumber, organizationNumber))
                .thenReturn(transactionList);
        when(parmSysDictSelfMapper.selectListByTypeId("AUTH_TRANSACTION_TYPE")).thenReturn(dictList);

        // When
        PageResultDTO<OutstandingTransactionDTO> result = outstandingTransService.queryOutStandingTransactionList(page, rows, cardNumber, organizationNumber);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        verify(outstandingTransactionSelfMapper).selectByCardNumberAndOrg(cardNumber, organizationNumber);
        verify(parmSysDictSelfMapper).selectListByTypeId("AUTH_TRANSACTION_TYPE");
    }

    @Test
    @DisplayName("根据ID查询未并账交易-成功")
    void testGetOutStandingTransactionById_Success() {
        // Given
        String outstandingTransactionId = "TEST_ID_001";
        when(outstandingTransactionMapper.selectByPrimaryKey(outstandingTransactionId)).thenReturn(testOutstandingTransaction);

        // When
        OutstandingTransactionDTO result = outstandingTransService.getOutStandingTransactionById(outstandingTransactionId);

        // Then
        assertNotNull(result);
        assertEquals(testOutstandingTransaction.getOutstandingTransactionId(), result.getOutstandingTransactionId());
        verify(outstandingTransactionMapper).selectByPrimaryKey(outstandingTransactionId);
    }

    @Test
    @DisplayName("根据ID查询未并账交易-未找到数据")
    void testGetOutStandingTransactionById_NotFound() {
        // Given
        String outstandingTransactionId = "TEST_ID_001";
        when(outstandingTransactionMapper.selectByPrimaryKey(outstandingTransactionId)).thenReturn(null);

        // When
        OutstandingTransactionDTO result = outstandingTransService.getOutStandingTransactionById(outstandingTransactionId);

        // Then
        assertNull(result);
        verify(outstandingTransactionMapper).selectByPrimaryKey(outstandingTransactionId);
    }

    @Test
    @DisplayName("根据管理账户ID查询未并账交易列表-成功")
    void testSelectOutstandingTransByManagementId_Success() {
        // Given
        String accountManagementId = "ACCOUNT_001";
        List<OutstandingTransaction> transactionList = Arrays.asList(testOutstandingTransaction);
        when(outstandingTransactionSelfMapper.selectByAccountManageIdList(accountManagementId)).thenReturn(transactionList);

        // When
        List<OutstandingTransactionDTO> result = outstandingTransService.selectOutstandingTransByManagementId(accountManagementId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(outstandingTransactionSelfMapper).selectByAccountManageIdList(accountManagementId);
    }

    @Test
    @DisplayName("根据管理账户ID查询未并账交易列表-参数为空")
    void testSelectOutstandingTransByManagementId_NullParam() {
        // Given
        String accountManagementId = null;

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.selectOutstandingTransByManagementId(accountManagementId));
        assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL.getCode(), exception.getErrCode());
        assertEquals("The incoming parameter is null:Query the unconjugated management account is empty", exception.getErrDetail());
    }

    @Test
    @DisplayName("根据入账标识查询未并账交易列表-成功")
    void testSelectOutstandingTransByPostIndicator_Success() {
        // Given
        String postIndicator = "Y";
        String organizationNumber = "ORG_001";
        List<OutstandingTransaction> transactionList = Arrays.asList(testOutstandingTransaction);
        when(outstandingTransactionSelfMapper.selectByPostIndicatorList(postIndicator, organizationNumber))
                .thenReturn(transactionList);

        // When
        List<OutstandingTransactionDTO> result = outstandingTransService.selectOutstandingTransByPostIndicator(postIndicator, organizationNumber);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(outstandingTransactionSelfMapper).selectByPostIndicatorList(postIndicator, organizationNumber);
    }

    @Test
    @DisplayName("根据入账标识查询未并账交易列表-参数为空")
    void testSelectOutstandingTransByPostIndicator_NullParam() {
        // Given
        String postIndicator = null;
        String organizationNumber = "ORG_001";

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.selectOutstandingTransByPostIndicator(postIndicator, organizationNumber));
        assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL.getCode(), exception.getErrCode());
        assertEquals("The incoming parameter is null:postIndicator is null", exception.getErrDetail());
    }

    @Test
    @DisplayName("根据卡号查询未并账交易列表-成功")
    void testSelectOutstandingTransByCardNumber_Success() {
        // Given
        String cardNumber = "1234567890123456";
        List<OutstandingTransaction> transactionList = Arrays.asList(testOutstandingTransaction);
        when(outstandingTransactionMapper.selectOutstandingTransBycardNumber(cardNumber)).thenReturn(transactionList);

        // When
        List<OutstandingTransactionDTO> result = outstandingTransService.selectOutstandingTransBycardNumber(cardNumber);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(outstandingTransactionMapper).selectOutstandingTransBycardNumber(cardNumber);
    }

    @Test
    @DisplayName("根据卡号查询未并账交易列表-数据库异常")
    void testSelectOutstandingTransByCardNumber_DatabaseException() {
        // Given
        String cardNumber = "1234567890123456";
        when(outstandingTransactionMapper.selectOutstandingTransBycardNumber(cardNumber))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.selectOutstandingTransBycardNumber(cardNumber));
        assertEquals(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR.getCode(), exception.getErrCode());
        verify(outstandingTransactionMapper).selectOutstandingTransBycardNumber(cardNumber);
    }

    @Test
    @DisplayName("根据ID删除未并账交易-成功")
    void testDelOutstandingTransactionById_Success() {
        // Given
        when(outstandingTransactionMapper.deleteByPrimaryKey(testOutstandingTransactionDTO.getOutstandingTransactionId())).thenReturn(1);
        when(outstandingTransactionHistoryMapper.insertSelective(any(OutstandingTransaction.class))).thenReturn(1);

        // When
        int result = outstandingTransService.delOutstandingTransactionById(testOutstandingTransactionDTO);

        // Then
        assertEquals(1, result);
        verify(outstandingTransactionMapper).deleteByPrimaryKey(testOutstandingTransactionDTO.getOutstandingTransactionId());
        verify(outstandingTransactionHistoryMapper).insertSelective(any(OutstandingTransaction.class));
    }

    @Test
    @DisplayName("插入未并账交易-成功")
    void testInsert_Success() {
        // Given
        when(outstandingTransactionMapper.insertSelective(any(OutstandingTransaction.class))).thenReturn(1);

        // When
        int result = outstandingTransService.insert(testOutstandingTransactionDTO);

        // Then
        assertEquals(1, result);
        verify(outstandingTransactionMapper).insertSelective(any(OutstandingTransaction.class));
    }

    @Test
    @DisplayName("插入未并账交易-参数为空")
    void testInsert_NullParam() {
        // Given
        OutstandingTransactionDTO outstandingTransactionDTO = null;

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.insert(outstandingTransactionDTO));
        assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL.getCode(), exception.getErrCode());
        assertEquals("The incoming parameter is null:outstandingTransactionDTO is null", exception.getErrDetail());
    }

    @Test
    @DisplayName("根据全局流水号查询未并账交易-成功")
    void testGetOutstandingTransactionByGlobalFlowNumber_Success() {
        // Given
        String globalFlowNumber = "GLOBAL_001";
        String organizationNumber = "ORG_001";
        when(outstandingTransactionSelfMapper.selectByGlobalFlowNumber(organizationNumber, globalFlowNumber))
                .thenReturn(testOutstandingTransaction);

        // When
        OutstandingTransactionDTO result = outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(globalFlowNumber, organizationNumber);

        // Then
        assertNotNull(result);
        assertEquals(testOutstandingTransaction.getOutstandingTransactionId(), result.getOutstandingTransactionId());
        verify(outstandingTransactionSelfMapper).selectByGlobalFlowNumber(organizationNumber, globalFlowNumber);
    }

    @Test
    @DisplayName("根据全局流水号查询未并账交易-参数为空")
    void testGetOutstandingTransactionByGlobalFlowNumber_NullParam() {
        // Given
        String globalFlowNumber = null;
        String organizationNumber = "ORG_001";

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(globalFlowNumber, organizationNumber));
        assertEquals(AnyTxnAuthRespCodeEnum.P_GLOBAL_FLOE_NUMBER.getCode(), exception.getErrCode());
        assertEquals("Global serial number is empty:globalFlowNumver is null", exception.getErrDetail());
    }

    @Test
    @DisplayName("更新未并账交易-成功")
    void testUpdate_Success() {
        // Given
        when(outstandingTransactionMapper.updateByPrimaryKeySelective(any(OutstandingTransaction.class))).thenReturn(1);

        // When
        int result = outstandingTransService.update(testOutstandingTransactionDTO);

        // Then
        assertEquals(1, result);
        verify(outstandingTransactionMapper).updateByPrimaryKeySelective(any(OutstandingTransaction.class));
    }

    @Test
    @DisplayName("更新未并账交易-参数为空")
    void testUpdate_NullParam() {
        // Given
        OutstandingTransactionDTO outstandingTransactionDTO = null;

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.update(outstandingTransactionDTO));
        assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL.getCode(), exception.getErrCode());
        assertEquals("The incoming parameter is null:outstandingTransactionDTO is null!", exception.getErrDetail());
    }

    @Test
    @DisplayName("根据入账标识和处理标志查询未并账交易列表-成功")
    void testSelectByPostIndicatorAndPostFlagList_Success() {
        // Given
        String postIndicator = "Y";
        String postFlag = "N";
        String organizationNumber = "ORG_001";
        List<OutstandingTransaction> transactionList = Arrays.asList(testOutstandingTransaction);
        when(outstandingTransactionSelfMapper.selectByPostIndicatorAndPostFlagList(organizationNumber, postIndicator, postFlag, 500))
                .thenReturn(transactionList);

        // When
        List<OutstandingTransactionDTO> result = outstandingTransService.selectByPostIndicatorAndPostFlagList(postIndicator, postFlag, organizationNumber);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(outstandingTransactionSelfMapper).selectByPostIndicatorAndPostFlagList(organizationNumber, postIndicator, postFlag, 500);
    }

    @Test
    @DisplayName("根据动态条件查询未并账交易列表-成功")
    void testSelectByDynamicConditions_Success() {
        // Given
        List<OutstandingTransaction> transactionList = Arrays.asList(testOutstandingTransaction);
        when(outstandingTransactionSelfMapper.selectByDynamicConditions(any(OutstandingTransaction.class)))
                .thenReturn(transactionList);

        // When
        List<OutstandingTransactionDTO> result = outstandingTransService.selectByDynamicConditions(testOutstandingTransactionDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(outstandingTransactionSelfMapper).selectByDynamicConditions(any(OutstandingTransaction.class));
    }

    @Test
    @DisplayName("备份未并账交易-成功")
    void testBackOutstandingTransaction_Success() {
        // Given
        when(outstandingTransactionHistoryMapper.insertSelective(any(OutstandingTransaction.class))).thenReturn(1);

        // When
        int result = outstandingTransService.backOutstandingTransaction(testOutstandingTransactionDTO);

        // Then
        assertEquals(1, result);
        verify(outstandingTransactionHistoryMapper).insertSelective(any(OutstandingTransaction.class));
    }

    @Test
    @DisplayName("根据管理账户ID、全局流水号和信贷节点ID查询未并账信息-成功")
    void testGetInfoByAccountManageIdAndGlobalFlowNumberAndCreditNodeId_Success() {
        // Given
        String accountManageId = "ACCOUNT_001";
        String globalFlowNumber = "GLOBAL_001";
        String creditNodeId = "CREDIT_001";
        when(outstandingTransactionSelfMapper.selectByAccountManageIdAndGlobalFlowNumberAndLimitNode(accountManageId, globalFlowNumber, creditNodeId))
                .thenReturn(testOutstandingTransaction);

        // When
        OutstandingTransactionDTO result = outstandingTransService.getInfoByAccountManageIdAndGlobalFlowNumberAndCreditNodeId(accountManageId, globalFlowNumber, creditNodeId);

        // Then
        assertNotNull(result);
        assertEquals(testOutstandingTransaction.getOutstandingTransactionId(), result.getOutstandingTransactionId());
        assertEquals(testOutstandingTransaction.getGlobalFlowNumber(), result.getGlobalFlowNumber());
        verify(outstandingTransactionSelfMapper).selectByAccountManageIdAndGlobalFlowNumberAndLimitNode(accountManageId, globalFlowNumber, creditNodeId);
    }

    @Test
    @DisplayName("根据管理账户ID、全局流水号和信贷节点ID查询未并账信息-未找到数据")
    void testGetInfoByAccountManageIdAndGlobalFlowNumberAndCreditNodeId_NotFound() {
        // Given
        String accountManageId = "ACCOUNT_001";
        String globalFlowNumber = "GLOBAL_001";
        String creditNodeId = "CREDIT_001";
        when(outstandingTransactionSelfMapper.selectByAccountManageIdAndGlobalFlowNumberAndLimitNode(accountManageId, globalFlowNumber, creditNodeId))
                .thenReturn(null);

        // When
        OutstandingTransactionDTO result = outstandingTransService.getInfoByAccountManageIdAndGlobalFlowNumberAndCreditNodeId(accountManageId, globalFlowNumber, creditNodeId);

        // Then
        assertNull(result);
        verify(outstandingTransactionSelfMapper).selectByAccountManageIdAndGlobalFlowNumberAndLimitNode(accountManageId, globalFlowNumber, creditNodeId);
    }

    @Test
    @DisplayName("根据管理账户ID、全局流水号和信贷节点ID查询未并账信息-数据库异常")
    void testGetInfoByAccountManageIdAndGlobalFlowNumberAndCreditNodeId_DatabaseException() {
        // Given
        String accountManageId = "ACCOUNT_001";
        String globalFlowNumber = "GLOBAL_001";
        String creditNodeId = "CREDIT_001";
        when(outstandingTransactionSelfMapper.selectByAccountManageIdAndGlobalFlowNumberAndLimitNode(accountManageId, globalFlowNumber, creditNodeId))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.getInfoByAccountManageIdAndGlobalFlowNumberAndCreditNodeId(accountManageId, globalFlowNumber, creditNodeId));
        assertEquals(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR.getCode(), exception.getErrCode());
        verify(outstandingTransactionSelfMapper).selectByAccountManageIdAndGlobalFlowNumberAndLimitNode(accountManageId, globalFlowNumber, creditNodeId);
    }

    @Test
    @DisplayName("根据全局流水号查询历史未并账交易-成功")
    void testGetOutstandingTransactionHistoryByGlobalFlowNumber_Success() {
        // Given
        String globalFlowNumber = "GLOBAL_001";
        String organizationNumber = "ORG_001";
        when(outstandingTransactionHistoryMapper.selectHistoryByGlobalFlowNumber(organizationNumber, globalFlowNumber))
                .thenReturn(testOutstandingTransaction);

        // When
        OutstandingTransactionDTO result = outstandingTransService.getOutstandingTransactionHistoryByGlobalFlowNumber(globalFlowNumber, organizationNumber);

        // Then
        assertNotNull(result);
        assertEquals(testOutstandingTransaction.getOutstandingTransactionId(), result.getOutstandingTransactionId());
        verify(outstandingTransactionHistoryMapper).selectHistoryByGlobalFlowNumber(organizationNumber, globalFlowNumber);
    }

    @Test
    @DisplayName("根据全局流水号查询历史未并账交易-未找到数据")
    void testGetOutstandingTransactionHistoryByGlobalFlowNumber_NotFound() {
        // Given
        String globalFlowNumber = "GLOBAL_001";
        String organizationNumber = "ORG_001";
        when(outstandingTransactionHistoryMapper.selectHistoryByGlobalFlowNumber(organizationNumber, globalFlowNumber))
                .thenReturn(null);

        // When
        OutstandingTransactionDTO result = outstandingTransService.getOutstandingTransactionHistoryByGlobalFlowNumber(globalFlowNumber, organizationNumber);

        // Then
        assertNull(result);
        verify(outstandingTransactionHistoryMapper).selectHistoryByGlobalFlowNumber(organizationNumber, globalFlowNumber);
    }

    @Test
    @DisplayName("根据全局流水号查询历史未并账交易-参数为空")
    void testGetOutstandingTransactionHistoryByGlobalFlowNumber_NullParam() {
        // Given
        String globalFlowNumber = null;
        String organizationNumber = "ORG_001";

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.getOutstandingTransactionHistoryByGlobalFlowNumber(globalFlowNumber, organizationNumber));
        assertEquals(AnyTxnAuthRespCodeEnum.P_GLOBAL_FLOE_NUMBER.getCode(), exception.getErrCode());
        assertEquals("Global serial number is empty:globalFlowNumver is null", exception.getErrDetail());
    }

    @Test
    @DisplayName("根据全局流水号查询历史未并账交易-数据库异常")
    void testGetOutstandingTransactionHistoryByGlobalFlowNumber_DatabaseException() {
        // Given
        String globalFlowNumber = "GLOBAL_001";
        String organizationNumber = "ORG_001";
        when(outstandingTransactionHistoryMapper.selectHistoryByGlobalFlowNumber(organizationNumber, globalFlowNumber))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () ->
                outstandingTransService.getOutstandingTransactionHistoryByGlobalFlowNumber(globalFlowNumber, organizationNumber));
        assertEquals(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR.getCode(), exception.getErrCode());
        verify(outstandingTransactionHistoryMapper).selectHistoryByGlobalFlowNumber(organizationNumber, globalFlowNumber);
    }
}